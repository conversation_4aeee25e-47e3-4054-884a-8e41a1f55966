# IMAP邮件附件处理深度分析报告

## 📋 执行概述

本报告基于对Onyx知识管理智能体工程的深度代码分析，重点分析了IMAP连接器的附件处理逻辑，以及与Vespa搜索引擎和MINIO文件存储的集成情况。

## 🏗️ 工程整体架构分析

### 核心组件架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Web)    │    │   后端 (Onyx)   │    │  存储层 (Vespa) │
│                 │    │                 │    │                 │
│ ImapEmailDetail │◄──►│ IMAP Connector  │◄──►│ Document Index  │
│ Panel.tsx       │    │ connector.py    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ 文件存储 (MINIO)│
                       │ S3BackedFileStore│
                       └─────────────────┘
```

### 技术栈分析
- **前端**: Next.js + TypeScript + React
- **后端**: Python + FastAPI + SQLAlchemy
- **搜索引擎**: Vespa (文档索引和检索)
- **文件存储**: S3兼容存储 (支持MINIO)
- **邮件处理**: Python imaplib + email库

## 🔍 IMAP连接器代码深度分析

### 1. 核心文件结构
```
backend/onyx/connectors/imap/
├── __init__.py
├── connector.py      # 主要连接器逻辑
└── models.py         # 邮件头模型定义
```

### 2. 邮件处理流程分析

#### 2.1 邮件获取流程
```python
# connector.py 第405-462行
def _fetch_email(mail_client: imaplib.IMAP4_SSL, email_id: str) -> Message | None:
    # 1. 通过IMAP获取原始邮件数据 (RFC822格式)
    status, msg_data = mail_client.fetch(message_set=email_id, message_parts="(RFC822)")
    
    # 2. 解析邮件数据为Message对象
    return email.message_from_bytes(raw_email)
```

#### 2.2 邮件内容解析流程
```python
# connector.py 第656-701行
def _parse_email_content_and_attachments(email_msg: Message, email_headers: EmailHeaders):
    """
    关键发现：此函数确实解析附件，但存在重大缺陷
    """
    for part in email_msg.walk():
        content_disposition = part.get_content_disposition()
        content_type = part.get_content_type()
        
        if content_disposition == 'attachment':
            # ✅ 正确识别附件
            filename = part.get_filename()
            attachment_size = len(part.get_payload(decode=True))
            attachments.append({
                'filename': filename,
                'content_type': content_type,
                'size': attachment_size
            })
            # ❌ 关键问题：只保存元信息，不保存附件内容！
```

### 3. 文档生成逻辑分析

#### 3.1 Document对象构建
```python
# connector.py 第464-543行
def _convert_email_headers_and_body_into_document():
    # 解析邮件正文和附件
    email_body, attachments = _parse_email_content_and_attachments(email_msg, email_headers)
    
    # 构建邮件元数据
    message_data = f"""
from: {email_headers.sender}
to: {email_headers.recipients or ''}
subject: {email_headers.subject}
date: {email_headers.date.isoformat()}"""
    
    # ✅ 附件信息被添加到元数据中
    if attachments:
        message_data += f"\nattachments: {len(attachments)} files"
        for att in attachments:
            message_data += f"\n- {att['filename']} ({att['content_type']}, {att['size']} bytes)"
    
    # ❌ 关键问题：附件信息只是文本，没有实际文件引用
    full_content = email_body + "\n" + message_data
    
    return Document(
        id=email_headers.id,
        title=email_headers.subject,
        sections=[TextSection(text=full_content)],  # 所有内容都作为文本存储
        source=DocumentSource.IMAP,
        # ❌ 没有利用Document模型的文件存储能力
    )
```

## 📊 附件处理逻辑深度分析

### 1. 当前实现的优缺点

#### ✅ 优点
1. **正确识别附件**: 能够识别`Content-Disposition: attachment`的邮件部分
2. **提取基本信息**: 正确提取文件名、类型、大小
3. **错误处理完善**: 有完整的异常处理和日志记录
4. **支持多种格式**: 支持各种附件类型的识别

#### ❌ 关键缺陷
1. **附件内容丢失**: 只保存元信息，不保存实际文件内容
2. **无法下载**: 前端显示"附件暂不支持下载"
3. **搜索局限**: 附件内容无法被索引和搜索
4. **存储浪费**: 没有利用现有的文件存储基础设施

### 2. 附件处理代码详细分析

#### 2.1 附件识别逻辑
```python
# 当前实现 (connector.py 第674-683行)
if content_disposition == 'attachment':
    filename = part.get_filename()
    if filename:
        attachment_size = len(part.get_payload(decode=True)) if part.get_payload(decode=True) else 0
        attachments.append({
            'filename': filename,
            'content_type': content_type,
            'size': attachment_size
        })
```

**问题分析**:
- `part.get_payload(decode=True)` 获取了附件的二进制内容
- 但这个内容只用于计算大小，然后就被丢弃了
- 没有将附件内容保存到文件存储系统

#### 2.2 改进方案设计
```python
# 建议的改进实现
def _extract_and_store_attachments(email_msg: Message, email_id: str) -> list[dict]:
    """提取并存储邮件附件到文件存储系统"""
    from onyx.file_store.file_store import get_default_file_store
    from onyx.configs.constants import FileOrigin
    
    file_store = get_default_file_store()
    attachments = []
    
    for part in email_msg.walk():
        if part.get_content_disposition() == 'attachment':
            filename = part.get_filename()
            if filename:
                # 获取附件内容
                attachment_content = part.get_payload(decode=True)
                content_type = part.get_content_type()
                
                # 保存到文件存储系统
                file_id = file_store.save_file(
                    content=BytesIO(attachment_content),
                    display_name=filename,
                    file_origin=FileOrigin.EMAIL_ATTACHMENT,
                    file_type=content_type,
                    file_metadata={
                        'email_id': email_id,
                        'original_filename': filename,
                        'source': 'imap_connector'
                    }
                )
                
                attachments.append({
                    'filename': filename,
                    'content_type': content_type,
                    'size': len(attachment_content),
                    'file_id': file_id,  # 关键：添加文件ID引用
                    'download_url': f'/api/files/{file_id}/download'
                })
    
    return attachments
```

## 🗄️ 存储策略深度分析

### 1. Vespa文档存储分析

#### 1.1 当前存储模式
```python
# Document模型 (models.py 第243-277行)
class Document(DocumentBase):
    id: str
    source: DocumentSource
    sections: list[TextSection | ImageSection]  # 支持文本和图片
    metadata: dict[str, str | list[str]]
    # ... 其他字段
```

**发现**:
- Document模型支持`ImageSection`，说明设计上支持文件存储
- 但IMAP连接器只使用了`TextSection`
- 附件信息只是作为文本存储在sections中

#### 1.2 Vespa索引能力
- Vespa主要用于文本搜索和语义检索
- 不适合存储大型二进制文件
- 适合存储文件元数据和文本内容

### 2. MINIO文件存储分析

#### 2.1 文件存储基础设施
```python
# file_store.py 第154-561行
class S3BackedFileStore(FileStore):
    """支持S3兼容存储 (包括MinIO)"""
    
    def save_file(self, content: IO, display_name: str, file_origin: FileOrigin, 
                  file_type: str, file_metadata: dict = None) -> str:
        # 完整的文件存储实现
        # 支持元数据、权限控制、版本管理
```

**发现**:
- 系统已有完整的文件存储基础设施
- 支持MINIO和其他S3兼容存储
- 有完整的文件元数据管理
- IMAP连接器完全没有使用这个能力

#### 2.2 文件存储配置
```python
# 环境变量配置
S3_FILE_STORE_BUCKET_NAME=<bucket-name>
S3_ENDPOINT_URL=<minio-endpoint>
S3_AWS_ACCESS_KEY_ID=<access-key>
S3_AWS_SECRET_ACCESS_KEY=<secret-key>
```

## 🖥️ 前端显示逻辑分析

### 1. ImapEmailDetailPanel.tsx分析

#### 1.1 附件解析逻辑
```typescript
// ImapEmailDetailPanel.tsx 第325-386行
// 附件信息提取 - 适用于三种格式
for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // 查找附件提及 - "附件为xxx" 或 "附件：xxx"
    if (trimmedLine.includes('附件为') || trimmedLine.includes('attachment')) {
        // 复杂的文本解析逻辑
    }
    
    // 增强的附件文件名提取 - 匹配更多模式
    const patterns = [
        /([^\s《》\[\]]+\.(png|jpg|jpeg|pdf|doc|docx|xls|xlsx|zip|rar|ppt|pptx))/gi,
        // ... 更多模式
    ];
}
```

**问题分析**:
- 前端需要通过复杂的文本解析来提取附件信息
- 这种方式不可靠，容易出错
- 无法处理非标准格式的附件信息

#### 1.2 附件显示逻辑
```typescript
// 附件列表显示 (第628-655行)
{emailDetail.attachments && emailDetail.attachments.length > 0 ? (
    <div className="space-y-2">
        {emailDetail.attachments.map((attachment: EmailAttachment, index: number) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center space-x-3">
                    <span className="text-gray-600">📎</span>
                    <div>
                        <div className="font-medium">{attachment.filename}</div>
                        <div className="text-sm text-gray-500">
                            {attachment.content_type} • {formatFileSize(attachment.size)}
                        </div>
                    </div>
                </div>
                <div className="text-sm text-gray-500">
                    附件暂不支持下载  {/* ❌ 硬编码的限制 */}
                </div>
            </div>
        ))}
    </div>
) : (
    <div className="text-sm text-gray-500 p-2 border rounded bg-gray-50">
        此邮件无附件
    </div>
)}
```

## 🔧 问题诊断和根本原因

### 1. 核心问题总结

| 问题类型 | 具体表现 | 根本原因 | 影响程度 |
|---------|---------|---------|---------|
| **附件内容丢失** | 只有元信息，无法下载 | IMAP连接器未保存附件内容 | 🔴 严重 |
| **前端解析复杂** | 需要复杂文本解析 | 后端未提供结构化附件数据 | 🟡 中等 |
| **搜索能力受限** | 附件内容无法搜索 | 附件未被索引 | 🟡 中等 |
| **用户体验差** | 显示"暂不支持下载" | 缺少完整的附件处理流程 | 🔴 严重 |

### 2. 技术债务分析

#### 2.1 设计层面
- IMAP连接器设计时未考虑附件存储
- 前后端接口设计不完整
- 缺少附件下载API

#### 2.2 实现层面
- 有文件存储基础设施但未使用
- 附件处理逻辑不完整
- 前端解析逻辑过于复杂

## 🚀 改进方案和实施建议

### 1. 短期改进方案 (1-2周)

#### 1.1 后端改进
```python
# 1. 修改IMAP连接器，添加附件存储
def _convert_email_headers_and_body_into_document():
    # 提取并存储附件
    stored_attachments = _extract_and_store_attachments(email_msg, email_headers.id)
    
    # 将附件信息添加到Document metadata
    document_metadata = {
        'attachments': json.dumps(stored_attachments) if stored_attachments else None
    }
    
    return Document(
        metadata=document_metadata,
        # ... 其他字段
    )

# 2. 添加附件下载API
@app.get("/api/email/{email_id}/attachments/{file_id}/download")
async def download_email_attachment(email_id: str, file_id: str):
    file_store = get_default_file_store()
    return file_store.read_file(file_id)
```

#### 1.2 前端改进
```typescript
// 1. 简化附件解析逻辑
const parseEmailAttachments = (document: OnyxDocument): EmailAttachment[] => {
    // 直接从metadata中获取附件信息
    const attachmentsJson = document.metadata?.attachments;
    if (attachmentsJson) {
        return JSON.parse(attachmentsJson);
    }
    return [];
};

// 2. 添加下载功能
const downloadAttachment = async (emailId: string, fileId: string, filename: string) => {
    const response = await fetch(`/api/email/${emailId}/attachments/${fileId}/download`);
    const blob = await response.blob();
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
};
```

### 2. 中期改进方案 (1个月)

#### 2.1 附件内容索引
```python
# 添加附件内容提取和索引
def _process_attachment_content(attachment_content: bytes, content_type: str) -> str:
    """提取附件文本内容用于索引"""
    if content_type.startswith('text/'):
        return attachment_content.decode('utf-8', errors='ignore')
    elif content_type == 'application/pdf':
        # 使用PDF解析库提取文本
        return extract_pdf_text(attachment_content)
    elif content_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
        # 使用Office文档解析库
        return extract_word_text(attachment_content)
    # ... 其他格式
    return ""
```

#### 2.2 搜索集成
```python
# 将附件作为独立文档索引
def _create_attachment_documents(attachments: list, email_doc: Document) -> list[Document]:
    attachment_docs = []
    for attachment in attachments:
        if attachment.get('text_content'):
            attachment_doc = Document(
                id=f"{email_doc.id}_attachment_{attachment['file_id']}",
                title=f"附件: {attachment['filename']}",
                sections=[TextSection(text=attachment['text_content'])],
                source=DocumentSource.IMAP,
                metadata={
                    'parent_email_id': email_doc.id,
                    'attachment_type': 'email_attachment',
                    'file_id': attachment['file_id']
                }
            )
            attachment_docs.append(attachment_doc)
    return attachment_docs
```

### 3. 长期改进方案 (2-3个月)

#### 3.1 统一附件管理系统
- 建立统一的附件管理API
- 支持附件预览功能
- 添加附件版本控制
- 实现附件权限管理

#### 3.2 智能附件处理
- 附件内容自动分类
- 重复附件去重
- 附件安全扫描
- 附件格式转换

## 📈 预期效果和收益

### 1. 用户体验提升
- ✅ 支持附件下载
- ✅ 附件内容可搜索
- ✅ 更好的附件管理界面

### 2. 系统能力增强
- ✅ 完整的邮件信息保存
- ✅ 更强的搜索能力
- ✅ 更好的数据完整性

### 3. 技术债务清理
- ✅ 简化前端解析逻辑
- ✅ 利用现有基础设施
- ✅ 提高代码可维护性

## 🎯 结论和建议

### 1. 核心发现
当前IMAP连接器**确实能够识别和解析邮件附件**，但存在**关键的实现缺陷**：
- 附件的二进制内容被丢弃，只保留元信息
- 没有利用系统现有的文件存储能力
- 前端需要复杂的文本解析来获取附件信息

### 2. 优先级建议
1. **高优先级**: 实现附件内容存储和下载功能
2. **中优先级**: 简化前端解析逻辑，改用结构化数据
3. **低优先级**: 添加附件内容索引和搜索功能

### 3. 实施路径
建议采用**渐进式改进**的方式：
1. 先修复核心的附件存储问题
2. 再优化用户界面和体验
3. 最后添加高级功能如内容搜索

通过这些改进，可以将IMAP连接器从"只能显示附件信息"升级为"完整的邮件附件管理系统"，大大提升用户体验和系统价值。

## 📝 详细技术实现方案

### 1. IMAP连接器改进实现

#### 1.1 新增文件存储导入
```python
# connector.py 顶部添加导入
from io import BytesIO
from onyx.file_store.file_store import get_default_file_store
from onyx.configs.constants import FileOrigin
import json
```

#### 1.2 改进的附件处理函数
```python
def _extract_and_store_attachments(
    email_msg: Message,
    email_id: str,
    db_session: Session | None = None
) -> list[dict]:
    """
    提取邮件附件并存储到文件系统
    返回附件元数据列表，包含文件ID用于后续下载
    """
    file_store = get_default_file_store()
    attachments = []
    attachment_count = 0

    logger.info(f"Email {email_id}: Starting attachment extraction")

    for part in email_msg.walk():
        if part.is_multipart():
            continue

        content_disposition = part.get_content_disposition()
        content_type = part.get_content_type()

        # 检查是否为附件
        if content_disposition == 'attachment' or part.get_filename():
            filename = part.get_filename()
            if not filename:
                attachment_count += 1
                filename = f"attachment_{attachment_count}"

            try:
                # 获取附件内容
                attachment_content = part.get_payload(decode=True)
                if not attachment_content:
                    logger.warning(f"Email {email_id}: Empty attachment {filename}")
                    continue

                # 生成唯一的文件ID
                file_id = f"{email_id}_att_{attachment_count}_{filename}"

                # 保存到文件存储系统
                stored_file_id = file_store.save_file(
                    content=BytesIO(attachment_content),
                    display_name=filename,
                    file_origin=FileOrigin.EMAIL_ATTACHMENT,
                    file_type=content_type or 'application/octet-stream',
                    file_metadata={
                        'email_id': email_id,
                        'original_filename': filename,
                        'source': 'imap_connector',
                        'attachment_index': attachment_count
                    },
                    file_id=file_id,
                    db_session=db_session
                )

                # 尝试提取文本内容用于搜索
                text_content = _extract_attachment_text_content(
                    attachment_content, content_type, filename
                )

                attachment_info = {
                    'filename': filename,
                    'content_type': content_type,
                    'size': len(attachment_content),
                    'file_id': stored_file_id,
                    'download_url': f'/api/files/{stored_file_id}/download',
                    'has_text_content': bool(text_content),
                    'text_preview': text_content[:200] if text_content else None
                }

                attachments.append(attachment_info)
                attachment_count += 1

                logger.info(f"Email {email_id}: Stored attachment {filename} "
                           f"({len(attachment_content)} bytes) as {stored_file_id}")

            except Exception as e:
                logger.error(f"Email {email_id}: Failed to process attachment {filename}: {e}")
                continue

    logger.info(f"Email {email_id}: Successfully processed {len(attachments)} attachments")
    return attachments

def _extract_attachment_text_content(content: bytes, content_type: str, filename: str) -> str:
    """
    从附件中提取文本内容用于搜索索引
    支持常见的文档格式
    """
    try:
        if content_type.startswith('text/'):
            # 纯文本文件
            return content.decode('utf-8', errors='ignore')
        elif content_type == 'application/pdf':
            # PDF文件 - 需要安装PyPDF2或pdfplumber
            try:
                import PyPDF2
                from io import BytesIO
                pdf_reader = PyPDF2.PdfReader(BytesIO(content))
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text()
                return text
            except ImportError:
                logger.warning("PyPDF2 not installed, cannot extract PDF text")
                return ""
        elif content_type in [
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword'
        ]:
            # Word文档 - 需要安装python-docx
            try:
                import docx
                from io import BytesIO
                doc = docx.Document(BytesIO(content))
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text
            except ImportError:
                logger.warning("python-docx not installed, cannot extract Word text")
                return ""
        elif content_type in [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ]:
            # Excel文件 - 需要安装openpyxl
            try:
                import openpyxl
                from io import BytesIO
                workbook = openpyxl.load_workbook(BytesIO(content))
                text = ""
                for sheet in workbook.worksheets:
                    for row in sheet.iter_rows(values_only=True):
                        text += " ".join([str(cell) for cell in row if cell]) + "\n"
                return text
            except ImportError:
                logger.warning("openpyxl not installed, cannot extract Excel text")
                return ""
        else:
            # 其他格式暂不支持文本提取
            return ""
    except Exception as e:
        logger.warning(f"Failed to extract text from {filename}: {e}")
        return ""
```

#### 1.3 修改文档转换函数
```python
def _convert_email_headers_and_body_into_document(
    email_msg: Message,
    email_headers: EmailHeaders,
    include_perm_sync: bool,
    db_session: Session | None = None,
) -> tuple[Document, list[Document]]:
    """
    转换邮件为文档对象，同时处理附件
    返回: (主邮件文档, 附件文档列表)
    """
    logger.info(f"🔍 DEBUG: Processing email with headers:")
    logger.info(f"  Subject: {repr(email_headers.subject)}")
    logger.info(f"  Sender: {repr(email_headers.sender)}")

    sender_name, sender_addr = _parse_singular_addr(raw_header=email_headers.sender)
    parsed_recipients = (
        _parse_addrs(raw_header=email_headers.recipients)
        if email_headers.recipients
        else []
    )

    expert_info_map = {
        recipient_addr: BasicExpertInfo(
            display_name=recipient_name, email=recipient_addr
        )
        for recipient_name, recipient_addr in parsed_recipients
    }
    if sender_addr not in expert_info_map:
        expert_info_map[sender_addr] = BasicExpertInfo(
            display_name=sender_name, email=sender_addr
        )

    # 解析邮件正文
    email_body = _parse_email_body(email_msg, email_headers)

    # 提取并存储附件
    attachments = _extract_and_store_attachments(email_msg, email_headers.id, db_session)

    # 构建完整的邮件元数据信息
    message_data = f"""
from: {email_headers.sender}
to: {email_headers.recipients or ''}"""

    if email_headers.cc_recipients:
        message_data += f"\ncc: {email_headers.cc_recipients}"

    if email_headers.bcc_recipients:
        message_data += f"\nbcc: {email_headers.bcc_recipients}"

    message_data += f"""
subject: {email_headers.subject}
date: {email_headers.date.isoformat()}"""

    # 添加附件信息到元数据
    document_metadata = {}
    if attachments:
        message_data += f"\nattachments: {len(attachments)} files"
        for att in attachments:
            message_data += f"\n- {att['filename']} ({att['content_type']}, {att['size']} bytes)"

        # 将附件信息存储为结构化数据
        document_metadata['attachments'] = json.dumps(attachments)
        document_metadata['attachment_count'] = str(len(attachments))

    # 合并邮件正文和元数据
    full_content = email_body + "\n" + message_data
    primary_owners = list(expert_info_map.values())
    external_access = (
        ExternalAccess(
            external_user_emails=set(expert_info_map.keys()),
            external_user_group_ids=set(),
            is_public=False,
        )
        if include_perm_sync
        else None
    )

    # 创建主邮件文档
    main_document = Document(
        id=email_headers.id,
        title=email_headers.subject,
        semantic_identifier=email_headers.subject,
        metadata=document_metadata,
        source=DocumentSource.IMAP,
        sections=[TextSection(text=full_content)],
        primary_owners=primary_owners,
        external_access=external_access,
    )

    # 创建附件文档（用于独立搜索）
    attachment_documents = []
    for attachment in attachments:
        if attachment.get('has_text_content') and attachment.get('text_preview'):
            attachment_doc = Document(
                id=f"{email_headers.id}_attachment_{attachment['file_id']}",
                title=f"附件: {attachment['filename']}",
                semantic_identifier=f"附件: {attachment['filename']} (来自邮件: {email_headers.subject})",
                metadata={
                    'parent_email_id': email_headers.id,
                    'attachment_type': 'email_attachment',
                    'file_id': attachment['file_id'],
                    'original_filename': attachment['filename'],
                    'content_type': attachment['content_type'],
                    'file_size': str(attachment['size'])
                },
                source=DocumentSource.IMAP,
                sections=[TextSection(text=attachment.get('text_preview', ''))],
                primary_owners=primary_owners,
                external_access=external_access,
            )
            attachment_documents.append(attachment_doc)

    return main_document, attachment_documents
```

### 2. 后端API改进

#### 2.1 新增附件下载API
```python
# 在适当的API文件中添加
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from onyx.file_store.file_store import get_default_file_store
from onyx.auth.users import current_user

router = APIRouter()

@router.get("/api/files/{file_id}/download")
async def download_file(
    file_id: str,
    user: User = Depends(current_user)
):
    """下载文件附件"""
    try:
        file_store = get_default_file_store()

        # 检查文件是否存在
        file_record = file_store.read_file_record(file_id)
        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")

        # 检查权限（可以根据需要添加更复杂的权限检查）
        # TODO: 添加基于邮件权限的文件访问控制

        # 读取文件内容
        file_content = file_store.read_file(file_id)

        # 返回文件流
        return StreamingResponse(
            file_content,
            media_type=file_record.file_type,
            headers={
                "Content-Disposition": f"attachment; filename={file_record.display_name}"
            }
        )
    except Exception as e:
        logger.error(f"Failed to download file {file_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to download file")

@router.get("/api/email/{email_id}/attachments")
async def get_email_attachments(
    email_id: str,
    user: User = Depends(current_user)
):
    """获取邮件的所有附件信息"""
    try:
        # 从文档索引中获取邮件信息
        document_index = get_default_document_index()
        email_doc = document_index.get_document_by_id(email_id)

        if not email_doc:
            raise HTTPException(status_code=404, detail="Email not found")

        # 解析附件信息
        attachments_json = email_doc.metadata.get('attachments')
        if attachments_json:
            attachments = json.loads(attachments_json)
            return {"attachments": attachments}
        else:
            return {"attachments": []}

    except Exception as e:
        logger.error(f"Failed to get attachments for email {email_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get attachments")
```

### 3. 前端改进实现

#### 3.1 简化的附件解析逻辑
```typescript
// lib/email/parser.ts
export interface EmailAttachment {
  filename: string;
  content_type: string;
  size: number;
  file_id?: string;
  download_url?: string;
  has_text_content?: boolean;
  text_preview?: string;
}

export const parseImapEmailAttachments = (document: OnyxDocument): EmailAttachment[] => {
  try {
    // 优先从metadata中获取结构化的附件信息
    const attachmentsJson = document.metadata?.attachments;
    if (attachmentsJson) {
      const attachments = JSON.parse(attachmentsJson);
      return attachments.map((att: any) => ({
        filename: att.filename,
        content_type: att.content_type,
        size: att.size,
        file_id: att.file_id,
        download_url: att.download_url,
        has_text_content: att.has_text_content,
        text_preview: att.text_preview
      }));
    }

    // 降级处理：如果没有结构化数据，使用原有的文本解析逻辑
    return parseAttachmentsFromText(document.content || '');
  } catch (error) {
    console.error('Failed to parse email attachments:', error);
    return [];
  }
};

// 保留原有的文本解析逻辑作为降级方案
const parseAttachmentsFromText = (content: string): EmailAttachment[] => {
  // 原有的复杂文本解析逻辑...
  // 这里保留现有的实现作为兼容性支持
};
```

#### 3.2 改进的附件下载功能
```typescript
// components/chat/ImapEmailDetailPanel.tsx
import { downloadFile } from '@/lib/api/files';

// 添加下载处理函数
const handleDownloadAttachment = async (attachment: EmailAttachment) => {
  if (!attachment.file_id) {
    toast.error('附件下载链接不可用');
    return;
  }

  try {
    setDownloadingAttachments(prev => ({ ...prev, [attachment.file_id!]: true }));

    await downloadFile(attachment.file_id, attachment.filename);

    toast.success(`附件 ${attachment.filename} 下载成功`);
  } catch (error) {
    console.error('Download failed:', error);
    toast.error(`下载失败: ${error.message}`);
  } finally {
    setDownloadingAttachments(prev => ({ ...prev, [attachment.file_id!]: false }));
  }
};

// 改进的附件显示组件
{emailDetail.attachments && emailDetail.attachments.length > 0 ? (
  <div className="space-y-2">
    {emailDetail.attachments.map((attachment: EmailAttachment, index: number) => (
      <div key={index} className="flex items-center justify-between p-3 border rounded bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-3">
          <span className="text-gray-600">📎</span>
          <div>
            <div className="font-medium">{attachment.filename}</div>
            <div className="text-sm text-gray-500">
              {attachment.content_type} • {formatFileSize(attachment.size)}
              {attachment.has_text_content && (
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                  可搜索
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {attachment.file_id ? (
            <button
              onClick={() => handleDownloadAttachment(attachment)}
              disabled={downloadingAttachments[attachment.file_id]}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {downloadingAttachments[attachment.file_id] ? '下载中...' : '下载'}
            </button>
          ) : (
            <span className="text-sm text-gray-500">
              附件不可用
            </span>
          )}
        </div>
      </div>
    ))}
  </div>
) : (
  <div className="text-sm text-gray-500 p-2 border rounded bg-gray-50">
    此邮件无附件
  </div>
)}
```

#### 3.3 文件下载工具函数
```typescript
// lib/api/files.ts
export const downloadFile = async (fileId: string, filename: string): Promise<void> => {
  try {
    const response = await fetch(`/api/files/${fileId}/download`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
      },
    });

    if (!response.ok) {
      throw new Error(`下载失败: ${response.statusText}`);
    }

    const blob = await response.blob();

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('File download failed:', error);
    throw error;
  }
};
```

## 🔄 迁移和部署策略

### 1. 数据迁移方案

#### 1.1 现有邮件重新处理
```python
# 迁移脚本：重新处理现有邮件的附件
async def migrate_existing_emails():
    """
    重新处理现有的IMAP邮件，提取并存储附件
    """
    document_index = get_default_document_index()

    # 查询所有IMAP来源的文档
    imap_documents = document_index.query_documents(
        source_filter=[DocumentSource.IMAP],
        limit=None
    )

    migrated_count = 0
    error_count = 0

    for doc in imap_documents:
        try:
            # 检查是否已经有附件信息
            if doc.metadata.get('attachments'):
                continue  # 已经处理过

            # 重新获取原始邮件（如果可能）
            # 这需要保存原始邮件ID和IMAP连接信息
            # 或者从现有内容中尝试解析附件信息

            # 更新文档元数据
            # ...

            migrated_count += 1
            logger.info(f"Migrated email {doc.id}")

        except Exception as e:
            error_count += 1
            logger.error(f"Failed to migrate email {doc.id}: {e}")

    logger.info(f"Migration completed: {migrated_count} migrated, {error_count} errors")
```

#### 1.2 渐进式部署
1. **阶段1**: 部署新代码，只对新邮件生效
2. **阶段2**: 运行迁移脚本处理历史邮件
3. **阶段3**: 验证功能正常后，清理旧的解析逻辑

### 2. 配置要求

#### 2.1 环境变量配置
```bash
# 文件存储配置
S3_FILE_STORE_BUCKET_NAME=onyx-email-attachments
S3_ENDPOINT_URL=http://minio:9000
S3_AWS_ACCESS_KEY_ID=your_access_key
S3_AWS_SECRET_ACCESS_KEY=your_secret_key

# 附件处理配置
MAX_ATTACHMENT_SIZE=50MB
ALLOWED_ATTACHMENT_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,png,zip,rar
EXTRACT_ATTACHMENT_TEXT=true
```

#### 2.2 依赖包安装
```bash
# 添加文档处理依赖
pip install PyPDF2 python-docx openpyxl python-pptx
```

## 📊 性能和监控

### 1. 性能考虑

#### 1.1 存储空间估算
- 平均邮件附件大小: 2MB
- 每日邮件量: 1000封
- 附件比例: 30%
- 每日存储需求: 1000 × 0.3 × 2MB = 600MB
- 年度存储需求: 600MB × 365 = 219GB

#### 1.2 性能优化
```python
# 异步处理大附件
async def process_large_attachment(attachment_content: bytes, metadata: dict):
    """异步处理大型附件，避免阻塞主流程"""
    if len(attachment_content) > 10 * 1024 * 1024:  # 10MB
        # 使用后台任务处理
        background_tasks.add_task(
            store_large_attachment,
            attachment_content,
            metadata
        )
        return {"status": "processing", "file_id": generate_temp_id()}
    else:
        # 同步处理小文件
        return store_attachment_immediately(attachment_content, metadata)
```

### 2. 监控指标

#### 2.1 关键指标
- 附件处理成功率
- 平均附件大小
- 存储空间使用量
- 下载请求频率
- 文本提取成功率

#### 2.2 告警设置
```python
# 监控配置示例
MONITORING_METRICS = {
    'attachment_processing_errors': {
        'threshold': 5,  # 每小时超过5个错误就告警
        'window': '1h'
    },
    'storage_usage': {
        'threshold': 0.8,  # 存储使用率超过80%告警
        'window': '1d'
    },
    'download_failures': {
        'threshold': 10,  # 每小时超过10个下载失败就告警
        'window': '1h'
    }
}
```

通过这个完整的技术实现方案，可以将IMAP连接器从当前的"附件信息展示"升级为"完整的附件管理系统"，实现真正的邮件附件存储、下载和搜索功能。
