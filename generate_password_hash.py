#!/usr/bin/env python3
"""
生成密码哈希工具
用于生成bcrypt哈希，供SQL脚本使用
"""

import sys
import os

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from fastapi_users.password import PasswordHelper

def generate_hash(password: str) -> str:
    """生成密码的bcrypt哈希"""
    password_helper = PasswordHelper()
    return password_helper.hash(password)

def main():
    password = "Abc@123!"
    
    print("=== 密码哈希生成工具 ===")
    print(f"原始密码: {password}")
    
    # 生成哈希
    hashed = generate_hash(password)
    print(f"bcrypt哈希: {hashed}")
    
    # 验证哈希
    password_helper = PasswordHelper()
    is_valid = password_helper.verify(password, hashed)
    print(f"哈希验证: {'✓ 正确' if is_valid else '✗ 错误'}")
    
    print("\n请将此哈希值复制到SQL脚本中替换现有的哈希值")

if __name__ == "__main__":
    main()