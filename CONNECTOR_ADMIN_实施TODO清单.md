# CONNECTOR_ADMIN 角色实施 TODO 清单

## 🚨 重要提醒
**在开始实施前，请确保已经充分理解了用户数据隔离的重要性！**
**本方案涉及重大架构变更，必须严格按照顺序执行，不可跳过任何步骤！**

---

## 📋 实施前准备工作

### 准备阶段：环境和备份
- [ ] **备份生产数据库** - 创建完整的数据库备份
- [ ] **准备测试环境** - 确保有独立的测试环境
- [ ] **代码分支管理** - 创建专用的功能分支
- [ ] **回滚方案准备** - 准备完整的回滚计划
- [ ] **团队沟通** - 通知相关团队成员实施计划

---

## 🔥 阶段一：数据库层面修改 (高风险，需要谨慎执行)

### 任务 1.1：修改 Connector 数据模型
- [ ] **文件**: `backend/onyx/db/models.py`
- [ ] **任务**: 在 `Connector` 类中添加 `creator_id` 字段
  ```python
  creator_id: Mapped[str] = mapped_column(
      ForeignKey("user.id"), 
      nullable=False,
      comment="连接器创建者的用户ID"
  )
  ```
- [ ] **任务**: 添加 `creator` 关系映射
  ```python
  creator: Mapped["User"] = relationship(
      "User", 
      back_populates="connectors",
      foreign_keys=[creator_id]
  )
  ```
- [ ] **验证**: 确保字段定义正确，外键约束有效
- [ ] **风险**: 可能影响现有的连接器查询逻辑

### 任务 1.2：修改 User 数据模型
- [ ] **文件**: `backend/onyx/db/models.py`
- [ ] **任务**: 在 `User` 类中添加 `connectors` 关系映射
  ```python
  connectors: Mapped[list["Connector"]] = relationship(
      "Connector",
      back_populates="creator",
      foreign_keys="Connector.creator_id"
  )
  ```
- [ ] **验证**: 确保双向关系映射正确
- [ ] **风险**: 可能影响用户相关的查询性能

### 任务 1.3：创建数据库迁移脚本
- [ ] **文件**: `backend/alembic/versions/xxx_add_connector_creator.py`
- [ ] **任务**: 创建 Alembic 迁移脚本
- [ ] **任务**: 添加 `creator_id` 字段（先允许为空）
- [ ] **任务**: 创建外键约束
- [ ] **任务**: 为现有连接器分配默认创建者
- [ ] **任务**: 设置字段为非空约束
- [ ] **验证**: 在测试环境中验证迁移脚本
- [ ] **风险**: 数据迁移失败可能导致系统无法启动

### 任务 1.4：处理现有数据的创建者分配
- [ ] **决策**: 确定现有连接器的创建者分配策略
  - [ ] 选项1：分配给第一个ADMIN用户
  - [ ] 选项2：分配给系统用户
  - [ ] 选项3：要求手动分配
- [ ] **任务**: 实施选定的分配策略
- [ ] **验证**: 确保所有现有连接器都有有效的创建者
- [ ] **风险**: 错误的分配可能导致权限混乱

---

## ⚙️ 阶段二：业务逻辑层面修改 (中等风险)

### 任务 2.1：修改连接器查询函数
- [ ] **文件**: `backend/onyx/db/connector.py`
- [ ] **任务**: 创建 `fetch_connectors_for_user` 函数
  ```python
  def fetch_connectors_for_user(
      db_session: Session,
      user: User,
      sources: list[DocumentSource] | None = None,
      input_types: list[InputType] | None = None,
  ) -> list[Connector]:
      # 实现用户级别的连接器过滤逻辑
  ```
- [ ] **任务**: 创建 `verify_connector_ownership` 函数
- [ ] **任务**: 修改现有的 `fetch_connectors` 调用
- [ ] **验证**: 确保不同角色用户看到正确的连接器列表
- [ ] **风险**: 过滤逻辑错误可能导致权限泄露

### 任务 2.2：修改连接器创建逻辑
- [ ] **文件**: `backend/onyx/server/documents/connector.py`
- [ ] **任务**: 在连接器创建时设置 `creator_id`
- [ ] **任务**: 验证创建者权限
- [ ] **验证**: 确保新创建的连接器正确关联到创建者
- [ ] **风险**: 创建逻辑错误可能导致连接器无法正确关联

### 任务 2.3：修改连接器操作逻辑
- [ ] **文件**: `backend/onyx/server/documents/connector.py`
- [ ] **任务**: 在所有连接器操作前验证所有权
- [ ] **任务**: 修改编辑、删除、运行等操作的权限检查
- [ ] **验证**: 确保用户只能操作自己的连接器
- [ ] **风险**: 权限检查不当可能导致越权操作

---

## 🔐 阶段三：权限系统修改 (低风险)

### 任务 3.1：添加新角色定义
- [ ] **文件**: `backend/onyx/auth/schemas.py`
- [ ] **任务**: 在 `UserRole` 枚举中添加 `CONNECTOR_ADMIN`
  ```python
  class UserRole(str, Enum):
      # ... 现有角色 ...
      CONNECTOR_ADMIN = "connector_admin"  # 新增角色
  ```
- [ ] **验证**: 确保角色定义正确
- [ ] **风险**: 角色定义错误可能导致权限系统异常

### 任务 3.2：添加权限检查函数
- [ ] **文件**: `backend/onyx/auth/users.py`
- [ ] **任务**: 创建 `current_connector_admin_or_above_user` 函数
- [ ] **任务**: 创建 `current_connector_admin_user` 函数
- [ ] **任务**: 实现用户级别的权限验证逻辑
- [ ] **验证**: 确保权限检查函数正确工作
- [ ] **风险**: 权限检查逻辑错误可能导致安全漏洞

---

## 🌐 阶段四：API端点修改 (中等风险)

### 任务 4.1：修改连接器管理端点
- [ ] **文件**: `backend/onyx/server/documents/connector.py`
- [ ] **任务**: 修改 `/admin/connector` (GET) 端点，使用用户过滤
- [ ] **任务**: 修改连接器创建端点，记录创建者
- [ ] **任务**: 修改连接器编辑端点，验证所有权
- [ ] **任务**: 修改连接器删除端点，验证所有权
- [ ] **任务**: 修改其他连接器相关端点的权限检查
- [ ] **验证**: 测试所有端点的权限控制
- [ ] **风险**: 端点修改错误可能导致功能异常

### 任务 4.2：修改 CC Pair 管理端点
- [ ] **文件**: `backend/onyx/server/documents/cc_pair.py`
- [ ] **任务**: 修改所有 CC Pair 相关端点的权限检查
- [ ] **任务**: 添加连接器所有权验证
- [ ] **验证**: 确保用户只能操作自己连接器的 CC Pair
- [ ] **风险**: CC Pair 权限控制错误可能导致数据泄露

### 任务 4.3：修改凭证管理端点 (🔧 需要修正)
- [ ] **文件**: `backend/onyx/server/documents/credential.py`
- [ ] **任务**: 修改凭证相关端点的权限检查
- [ ] **任务**: **修正凭证删除端点** - 允许 CONNECTOR_ADMIN 删除自己的凭证
- [ ] **任务**: 添加凭证与连接器关联的验证
- [ ] **验证**: 确保用户只能管理自己连接器的凭证
- [ ] **风险**: 凭证权限控制错误可能导致敏感信息泄露

---

## 🎨 阶段五：前端界面修改 (低风险但关键)

### 任务 5.1：添加前端角色定义
- [ ] **文件**: `web/src/lib/types.ts`
- [ ] **任务**: 在 `UserRole` 枚举中添加 `CONNECTOR_ADMIN`
- [ ] **任务**: 在 `USER_ROLE_LABELS` 中添加对应标签
- [ ] **验证**: 确保前端角色定义与后端一致
- [ ] **风险**: 前后端角色不一致可能导致界面异常

### 任务 5.2：修改管理面板菜单权限 (🚨 关键修复)
- [ ] **文件**: `web/src/components/admin/ClientLayout.tsx`
- [ ] **任务**: 添加 `isConnectorAdmin` 角色判断逻辑
  ```typescript
  const isConnectorAdmin = user?.role === UserRole.CONNECTOR_ADMIN;
  ```
- [ ] **任务**: 为 CONNECTOR_ADMIN 创建专用菜单集合
- [ ] **任务**: 限制 CONNECTOR_ADMIN 只能看到连接器相关菜单
- [ ] **任务**: 隐藏用户管理、系统配置等菜单
- [ ] **验证**: 确保 CONNECTOR_ADMIN 用户只看到允许的菜单项
- [ ] **风险**: 菜单权限错误可能导致越权访问

### 任务 5.3：添加前端路由保护
- [ ] **文件**: `web/src/components/admin/Layout.tsx`
- [ ] **任务**: 添加 CONNECTOR_ADMIN 路由重定向逻辑
- [ ] **任务**: 定义允许访问的路径白名单
- [ ] **任务**: 实现强制重定向到允许的页面
- [ ] **验证**: 测试直接访问受限路由会被重定向
- [ ] **风险**: 路由保护不当可能被绕过

### 任务 5.4：修改连接器列表界面
- [ ] **文件**: 连接器相关的前端组件
- [ ] **任务**: 添加连接器创建者信息显示
- [ ] **任务**: 添加所有权标识
- [ ] **任务**: 修改权限相关的界面逻辑
- [ ] **验证**: 确保界面正确显示用户权限范围
- [ ] **风险**: 界面显示错误可能误导用户

---

## 🧪 阶段六：测试和验证 (关键阶段)

### 任务 6.1：单元测试
- [ ] **任务**: 为新的权限检查函数编写单元测试
- [ ] **任务**: 为连接器查询函数编写单元测试
- [ ] **任务**: 为所有权验证函数编写单元测试
- [ ] **验证**: 确保所有测试通过
- [ ] **风险**: 测试不充分可能遗漏关键问题

### 任务 6.2：集成测试
- [ ] **任务**: 测试不同角色用户的连接器访问权限
- [ ] **任务**: 测试连接器创建、编辑、删除的权限控制
- [ ] **任务**: 测试 API 端点的权限验证
- [ ] **验证**: 确保权限隔离正确工作
- [ ] **风险**: 集成测试不充分可能导致生产环境问题

### 任务 6.3：端到端测试
- [ ] **任务**: 测试完整的用户工作流程
- [ ] **任务**: 测试不同角色用户的界面访问
- [ ] **任务**: 测试权限边界情况
- [ ] **验证**: 确保用户体验符合预期
- [ ] **风险**: 端到端测试不充分可能影响用户体验

### 任务 6.4：安全渗透测试 (🔒 必须执行)
- [ ] **任务**: 测试 CONNECTOR_ADMIN 无法访问用户管理功能
- [ ] **任务**: 测试 CONNECTOR_ADMIN 无法访问系统配置功能
- [ ] **任务**: 测试 CONNECTOR_ADMIN 无法看到其他用户的连接器
- [ ] **任务**: 测试前端路由绕过防护
- [ ] **任务**: 测试 API 权限边界
- [ ] **验证**: 确保所有安全边界都有效
- [ ] **风险**: 安全测试不充分可能导致权限泄露

---

## 🚀 阶段七：部署和监控 (高风险)

### 任务 7.1：数据库迁移部署
- [ ] **任务**: 在生产环境执行数据库迁移
- [ ] **任务**: 验证迁移结果
- [ ] **任务**: 监控系统性能
- [ ] **备份**: 确保有完整的数据库备份
- [ ] **回滚**: 准备回滚方案
- [ ] **风险**: 迁移失败可能导致系统不可用

### 任务 7.2：应用部署
- [ ] **任务**: 部署后端代码更改
- [ ] **任务**: 部署前端代码更改
- [ ] **任务**: 验证部署结果
- [ ] **监控**: 监控系统日志和错误
- [ ] **风险**: 部署错误可能导致功能异常

### 任务 7.3：生产环境验证
- [ ] **任务**: 验证新角色功能正常工作
- [ ] **任务**: 验证现有功能未受影响
- [ ] **任务**: 验证权限隔离正确实施
- [ ] **监控**: 持续监控系统稳定性
- [ ] **风险**: 生产环境问题可能影响所有用户

---

## ⚠️ 关键检查点

### 每个阶段完成后必须验证：
- [ ] 代码审查通过
- [ ] 安全测试通过
- [ ] 权限边界验证通过
- [ ] 性能影响评估通过
- [ ] 回滚方案准备完成

### 🚨 实施前最终确认：
- [ ] 已充分理解用户数据隔离的重要性
- [ ] 已准备完整的数据库备份
- [ ] 已准备回滚方案
- [ ] 团队已充分沟通实施计划
- [ ] 测试环境已验证所有功能

---

## 📊 预期结果

实施完成后，CONNECTOR_ADMIN 用户将拥有：

✅ **完整的连接器管理功能**：
- 创建、查看、编辑、删除自己的连接器
- 管理自己连接器的凭证和 CC Pair
- 监控自己连接器的状态和性能

✅ **严格的权限边界**：
- 只能看到自己创建的连接器
- 无法访问用户管理功能
- 无法访问系统配置功能
- 无法看到其他用户的任何信息

✅ **安全的用户体验**：
- 管理面板只显示允许的菜单
- 直接访问受限页面会被重定向
- 界面清晰显示权限范围

**这将完全满足您的原始需求：能管理连接器，但看不到其他用户信息！**
