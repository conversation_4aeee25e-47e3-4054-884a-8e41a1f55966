-- 批量创建用户SQL脚本
-- 注意：密码哈希是通过bcrypt生成的 "Abc@123!" 的哈希值
-- 如果要修改密码，需要重新生成bcrypt哈希

-- 插入用户数据
INSERT INTO "user" (
    id, 
    email, 
    hashed_password, 
    is_active, 
    is_superuser, 
    is_verified, 
    role,
    shortcut_enabled,
    chosen_assistants,
    visible_assistants,
    hidden_assistants,
    pinned_assistants
) VALUES 
-- 管理员用户
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewGKgwAJ.fGKHnYS', -- Abc@123!
    true,
    true,
    true,
    'ADMIN',
    false,
    null,
    '[]'::jsonb,
    '[]'::jsonb,
    null
),
-- 普通用户 - 请根据实际需要修改邮箱地址
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewGKgwAJ.fGKHnYS', -- Abc@123!
    true,
    false,
    true,
    'BASIC',
    false,
    null,
    '[]'::jsonb,
    '[]'::jsonb,
    null
),
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewGKgwAJ.fGKHnYS', -- Abc@123!
    true,
    false,
    true,
    'BASIC',
    false,
    null,
    '[]'::jsonb,
    '[]'::jsonb,
    null
),
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewGKgwAJ.fGKHnYS', -- Abc@123!
    true,
    false,
    true,
    'BASIC',
    false,
    null,
    '[]'::jsonb,
    '[]'::jsonb,
    null
);

-- 添加更多用户，复制上面的格式，修改邮箱地址即可
-- 例如：
/*
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewGKgwAJ.fGKHnYS', -- Abc@123!
    true,
    false,
    true,
    'BASIC',
    false,
    null,
    '[]'::jsonb,
    '[]'::jsonb,
    null
);
*/

-- 查询创建的用户
SELECT id, email, role, is_active, is_verified FROM "user" 
WHERE email LIKE '%@cmsr.chinamobile.com' 
ORDER BY email;