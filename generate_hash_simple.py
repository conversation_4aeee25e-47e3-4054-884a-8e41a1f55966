#!/usr/bin/env python3
"""
简单的密码哈希生成工具
无需依赖项目环境
"""

import bcrypt

def generate_bcrypt_hash(password: str) -> str:
    """生成bcrypt哈希"""
    # 使用12轮，与FastAPI-Users默认设置一致
    salt = bcrypt.gensalt(rounds=12)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_hash(password: str, hashed: str) -> bool:
    """验证密码哈希"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def main():
    password = "Sygy@2025!"
    
    print("=== 密码哈希生成工具 ===")
    print(f"原始密码: {password}")
    
    # 生成哈希
    hashed = generate_bcrypt_hash(password)
    print(f"bcrypt哈希: {hashed}")
    
    # 验证哈希
    is_valid = verify_hash(password, hashed)
    print(f"哈希验证: {'✓ 正确' if is_valid else '✗ 错误'}")
    
    print("\n=== 使用说明 ===")
    print("1. 复制上面的bcrypt哈希值")
    print("2. 替换SQL脚本中的密码哈希")
    print("3. 在PostgreSQL中执行SQL脚本")

if __name__ == "__main__":
    main()