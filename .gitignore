# editors
.vscode
.zed

# macos
.DS_store

# python
.venv
.mypy_cache
.idea

# testing
/web/test-results/
backend/onyx/agent_search/main/test_data.json
backend/tests/regression/answer_quality/test_data.json
backend/tests/regression/search_quality/eval-*
backend/tests/regression/search_quality/search_eval_config.yaml
backend/tests/regression/search_quality/*.json

# secret files
.env
.env.imap
.env.main
jira_test_env

# others
/deployment/data/nginx/app.conf
*.sw?
/backend/tests/regression/answer_quality/search_test_config.yaml

settings.local.json
