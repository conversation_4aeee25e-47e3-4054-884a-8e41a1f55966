-- 插入2个测试用户到user表
-- 密码: Sygy@2025!
-- 注意：请在PostgreSQL中执行此脚本

-- 检查是否已有这些用户
SELECT email FROM "user" WHERE email IN ('<EMAIL>', '<EMAIL>');

-- 插入测试用户
INSERT INTO "user" (
    id, 
    email, 
    hashed_password, 
    is_active, 
    is_superuser, 
    is_verified, 
    role,
    shortcut_enabled,
    chosen_assistants,
    visible_assistants,
    hidden_assistants,
    pinned_assistants
) VALUES 
-- 管理员用户
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$ib1u24mS4cwHKaxxdgP3yOEt.pZpVDmf9h0IlzztI4vFF0vMCJejq', -- Sygy@2025! 的哈希值（真实值）
    true,    -- is_active
    true,    -- is_superuser
    true,    -- is_verified
    'ADMIN', -- role
    false,   -- shortcut_enabled
    null,    -- chosen_assistants
    '[]'::jsonb,  -- visible_assistants
    '[]'::jsonb,  -- hidden_assistants
    null     -- pinned_assistants
),
-- 普通用户
(
    gen_random_uuid(),
    '<EMAIL>',
    '$2b$12$ib1u24mS4cwHKaxxdgP3yOEt.pZpVDmf9h0IlzztI4vFF0vMCJejq', -- Sygy@2025! 的哈希值（真实值）
    true,     -- is_active
    false,    -- is_superuser
    true,     -- is_verified
    'BASIC',  -- role
    false,    -- shortcut_enabled
    null,     -- chosen_assistants
    '[]'::jsonb,  -- visible_assistants
    '[]'::jsonb,  -- hidden_assistants
    null      -- pinned_assistants
);

-- 验证插入结果
SELECT 
    id, 
    email, 
    role, 
    is_active, 
    is_superuser, 
    is_verified,
    length(hashed_password) as password_hash_length
FROM "user" 
WHERE email LIKE '%@cmsr.chinamobile.com' 
ORDER BY email;

-- 显示创建的用户数量
SELECT COUNT(*) as total_company_users 
FROM "user" 
WHERE email LIKE '%@cmsr.chinamobile.com';