version: '3.8'

services:
  # Vespa搜索引擎
  vespa:
    image: vespaengine/vespa:8.526.15
    container_name: km-vespa
    hostname: km-vespa
    restart: unless-stopped
    ports:
      - "8081:8080"
      - "19071:19071"
    volumes:
      - vespa_data:/opt/vespa/var
    environment:
      - VESPA_CONFIGSERVERS=km-vespa  # ✅ 修复
      - VESPA_SKIP_UPGRADE_CHECK=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/ApplicationStatus"]
      interval: 45s
      timeout: 20s
      retries: 10
      start_period: 300s
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '1.0'
          memory: 4G
    logging:  # ✅ 添加日志配置
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # AI模型服务器（本地处理）
  model_server:
    build:
      context: .
      dockerfile: docker/Dockerfile.model-server
    container_name: km-model-server
    restart: unless-stopped
    ports:
      - "9000:9000"
    environment:
      - REDIS_URL=redis://**********:6379/1
      - MODEL_CACHE_DIR=/app/model_cache
      - PYTHONPATH=/app
      - MODEL_SERVER_HOST=0.0.0.0
      - MODEL_SERVER_PORT=9000
      - INDEXING_ONLY=false
    volumes:
      - model_cache:/app/model_cache
      - /opt/km/logs:/app/logs
    depends_on:
      - dependency_check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # API后端服务
  api_server:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: km-api-server
    restart: unless-stopped
    ports:
      - "8080:8080"
    env_file:
      - .env.main
    depends_on:
      vespa:
        condition: service_healthy
      model_server:
        condition: service_healthy
    volumes:
      - /opt/km/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    security_opt:  # ✅ 添加安全配置
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # 后台任务处理
  background:
    build:
      context: .  # ✅ 修复：使用项目根目录作为构建上下文
      dockerfile: docker/Dockerfile.worker
    container_name: km-background
    restart: unless-stopped
    env_file:
      - .env.main
    depends_on:
      api_server:
        condition: service_healthy
    volumes:
      - background_logs:/app/logs
      - model_cache:/app/model_cache
    deploy:  # ✅ 添加资源限制
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # Web前端服务
  web_server:
    build:
      context: .  # ✅ 修复：使用项目根目录作为构建上下文
      dockerfile: docker/Dockerfile.web
    container_name: km-web-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.main
    depends_on:
      api_server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]  # ✅ 修复健康检查路径
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s  # ✅ 增加启动等待时间
    deploy:
      resources:
        limits:
          cpus: '2.0'  # ✅ 增加CPU限制
          memory: 2G   # ✅ 增加内存限制
        reservations:
          cpus: '0.5'  # ✅ 增加CPU预留
          memory: 512M # ✅ 增加内存预留
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # Nginx反向代理
  nginx:
    image: nginx:1.25-alpine  # ✅ 固定版本
    container_name: km-nginx
    restart: unless-stopped
    ports:
      - "18080:80"
      - "443:443"
    volumes:
      - nginx_config:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      web_server:
        condition: service_healthy
      api_server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    security_opt:
      - no-new-privileges:true
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "6"
    networks:
      - km_network

  # ✅ 添加外部依赖检查服务
  dependency_check:
    image: alpine:latest
    container_name: km-dependency-check
    command: >
      sh -c "
        apk add --no-cache curl postgresql-client redis &&
        echo 'Checking external dependencies...' &&
        until pg_isready -h ********** -p 5432 -U km_user; do
          echo 'Waiting for PostgreSQL...'; sleep 5;
        done &&
        until redis-cli -h ********** -p 6379 ping; do
          echo 'Waiting for Redis...'; sleep 5;
        done &&
        until curl -f http://**********:9001; do
          echo 'Waiting for MinIO...'; sleep 5;
        done &&
        echo 'All external dependencies are ready!'
      "
    networks:
      - km_network

# ✅ 定义数据卷
volumes:
  vespa_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/vespa
  api_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/api
  background_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/background
  model_cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/model_cache
  nginx_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/nginx/conf.d
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/km/logs/nginx

networks:
  km_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
