#!/usr/bin/env python3
"""
批量创建用户脚本
用于预置公司员工邮箱账号到Onyx知识管理系统
"""

import asyncio
import sys
import os
from typing import List

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi_users.password import PasswordHelper
import uuid

from onyx.db.models import User
from onyx.auth.schemas import UserRole
from onyx.configs.app_configs import POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB

# 数据库连接配置
DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# 员工邮箱列表 - 请根据实际情况修改
EMPLOYEE_EMAILS = [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    # 在这里添加更多员工邮箱...
    # 您可以从Excel或CSV文件中复制邮箱列表
    # 格式示例：
    # "<EMAIL>",
    # "<EMAIL>",
    # "<EMAIL>",
]

# 统一初始密码
INITIAL_PASSWORD = "Abc@123!"

class UserBatchCreator:
    def __init__(self):
        self.engine = create_async_engine(DATABASE_URL)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self.password_helper = PasswordHelper()

    async def create_user_batch(self, emails: List[str], password: str):
        """批量创建用户"""
        async with self.async_session() as session:
            created_users = []
            failed_users = []
            
            for email in emails:
                try:
                    # 检查用户是否已存在
                    existing_user = await session.get(User, {"email": email})
                    if existing_user:
                        print(f"用户 {email} 已存在，跳过创建")
                        continue
                    
                    # 创建新用户
                    hashed_password = self.password_helper.hash(password)
                    
                    user = User(
                        id=uuid.uuid4(),
                        email=email.lower(),
                        hashed_password=hashed_password,
                        is_active=True,
                        is_superuser=False,
                        is_verified=True,  # 预置用户直接设为已验证
                        role=UserRole.BASIC,  # 默认角色为基础用户
                        shortcut_enabled=False,
                        chosen_assistants=None,
                        visible_assistants=[],
                        hidden_assistants=[],
                        pinned_assistants=None
                    )
                    
                    session.add(user)
                    created_users.append(email)
                    print(f"✓ 创建用户: {email}")
                    
                except Exception as e:
                    failed_users.append((email, str(e)))
                    print(f"✗ 创建用户失败: {email}, 错误: {e}")
            
            try:
                await session.commit()
                print(f"\n成功创建 {len(created_users)} 个用户")
                if failed_users:
                    print(f"创建失败 {len(failed_users)} 个用户:")
                    for email, error in failed_users:
                        print(f"  - {email}: {error}")
                        
            except Exception as e:
                await session.rollback()
                print(f"批量创建失败，事务已回滚: {e}")

    async def create_admin_user(self, email: str, password: str):
        """创建管理员用户"""
        async with self.async_session() as session:
            try:
                # 检查管理员是否已存在
                existing_user = await session.get(User, {"email": email})
                if existing_user:
                    print(f"管理员用户 {email} 已存在")
                    return
                
                hashed_password = self.password_helper.hash(password)
                
                admin_user = User(
                    id=uuid.uuid4(),
                    email=email.lower(),
                    hashed_password=hashed_password,
                    is_active=True,
                    is_superuser=True,
                    is_verified=True,
                    role=UserRole.ADMIN,
                    shortcut_enabled=False,
                    chosen_assistants=None,
                    visible_assistants=[],
                    hidden_assistants=[],
                    pinned_assistants=None
                )
                
                session.add(admin_user)
                await session.commit()
                print(f"✓ 创建管理员用户: {email}")
                
            except Exception as e:
                await session.rollback()
                print(f"✗ 创建管理员失败: {email}, 错误: {e}")

    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()

async def main():
    print("=== Onyx 用户批量创建工具 ===")
    print(f"初始密码: {INITIAL_PASSWORD}")
    print(f"待创建用户数量: {len(EMPLOYEE_EMAILS)}")
    print()
    
    # 确认操作
    confirm = input("确认要执行批量用户创建吗? (y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    creator = UserBatchCreator()
    
    try:
        # 首先创建一个管理员用户（如果需要）
        admin_email = input("请输入管理员邮箱 (留空跳过): ").strip()
        if admin_email:
            await creator.create_admin_user(admin_email, INITIAL_PASSWORD)
            print()
        
        # 批量创建普通用户
        print("开始批量创建用户...")
        await creator.create_user_batch(EMPLOYEE_EMAILS, INITIAL_PASSWORD)
        
        print("\n=== 创建完成 ===")
        print("所有用户的初始密码为:", INITIAL_PASSWORD)
        print("建议用户首次登录后修改密码")
        
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    finally:
        await creator.close()

if __name__ == "__main__":
    asyncio.run(main())