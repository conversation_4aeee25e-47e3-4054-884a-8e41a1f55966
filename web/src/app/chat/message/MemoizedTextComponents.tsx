import {
  Citation,
  QuestionCardProps,
  DocumentCardProps,
} from "@/components/search/results/Citation";
import { LoadedOnyxDocument, OnyxDocument } from "@/lib/search/interfaces";
import React, { memo } from "react";
import isEqual from "lodash/isEqual";
import { SourceIcon } from "@/components/SourceIcon";
import { WebResultIcon } from "@/components/WebResultIcon";
import { SubQuestionDetail } from "../interfaces";
import { ValidSources } from "@/lib/types";
import { FileResponse } from "../my-documents/DocumentsContext";
import { ImapCitation } from "@/components/chat/ImapCitation";
import { isImapEmail } from "@/lib/email/utils";

export const MemoizedAnchor = memo(
  ({
    docs,
    subQuestions,
    openQuestion,
    userFiles,
    href,
    updatePresentingDocument,
    onImapEmailDetailClick,
    children,
  }: {
    subQuestions?: SubQuestionDetail[];
    openQuestion?: (question: SubQuestionDetail) => void;
    docs?: OnyxDocument[] | null;
    userFiles?: FileResponse[] | null;
    updatePresentingDocument: (doc: OnyxDocument) => void;
    onImapEmailDetailClick?: (document: OnyxDocument) => void;
    href?: string;
    children: React.ReactNode;
  }): JSX.Element => {
    console.log('⚓ [MemoizedAnchor] Component rendered:', {
      childrenValue: children?.toString(),
      docsCount: docs?.length || 0,
      hasImapCallback: !!onImapEmailDetailClick,
      hasUserFiles: !!(userFiles && userFiles.length > 0)
    });
    const value = children?.toString();
    if (value?.startsWith("[") && value?.endsWith("]")) {
      const match = value.match(/\[(D|Q)?(\d+)\]/);
      console.log('🔍 [MemoizedAnchor] Citation pattern detected:', {
        value,
        match: match ? match[0] : null,
        matchType: match ? match[1] : null,
        matchNumber: match ? match[2] : null
      });
      
      if (match) {
        const match_item = match[2];
        if (match_item !== undefined) {
          const isUserFileCitation = userFiles?.length && userFiles.length > 0;
          if (isUserFileCitation) {
            const index = Math.min(
              parseInt(match_item, 10) - 1,
              userFiles?.length - 1
            );
            const associatedUserFile = userFiles?.[index];
            if (!associatedUserFile) {
              return <a href={children as string}>{children}</a>;
            }
          } else if (!isUserFileCitation) {
            const index = parseInt(match_item, 10) - 1;
            const associatedDoc = docs?.[index];
            if (!associatedDoc) {
              return <a href={children as string}>{children}</a>;
            }
          } else {
            const index = parseInt(match_item, 10) - 1;
            const associatedSubQuestion = subQuestions?.[index];
            if (!associatedSubQuestion) {
              return <a href={href || (children as string)}>{children}</a>;
            }
          }
        }
      }

      if (match) {
        const match_item = match[2];
        if (match_item !== undefined) {
          const isSubQuestion = match[1] === "Q";
          const isDocument = !isSubQuestion;

          // Fix: parseInt now uses match[2], which is the numeric part
          const index = parseInt(match_item, 10) - 1;

          const associatedDoc = isDocument ? docs?.[index] : null;
          const associatedSubQuestion = isSubQuestion
            ? subQuestions?.[index]
            : undefined;

          if (!associatedDoc && !associatedSubQuestion) {
            return <>{children}</>;
          }

          let icon: React.ReactNode = null;
          if (associatedDoc?.source_type === "web") {
            icon = <WebResultIcon url={associatedDoc.link} />;
          } else {
            icon = (
              <SourceIcon
                sourceType={associatedDoc?.source_type as ValidSources}
                iconSize={18}
              />
            );
          }
          const associatedDocInfo = associatedDoc
            ? {
                ...associatedDoc,
                icon: icon as any,
                link: associatedDoc.link,
              }
            : undefined;

          return (
            <MemoizedLink
              updatePresentingDocument={updatePresentingDocument}
              href={href}
              document={associatedDocInfo}
              question={associatedSubQuestion}
              openQuestion={openQuestion}
              onImapEmailDetailClick={onImapEmailDetailClick}
            >
              {children}
            </MemoizedLink>
          );
        }
      }
    }
    return (
      <MemoizedLink
        updatePresentingDocument={updatePresentingDocument}
        href={href}
        onImapEmailDetailClick={onImapEmailDetailClick}
      >
        {children}
      </MemoizedLink>
    );
  }
);

export const MemoizedLink = memo(
  ({
    node,
    document,
    updatePresentingDocument,
    question,
    href,
    openQuestion,
    onImapEmailDetailClick,
    ...rest
  }: Partial<DocumentCardProps & QuestionCardProps> & {
    node?: any;
    onImapEmailDetailClick?: (document: OnyxDocument) => void;
    [key: string]: any;
  }) => {
    console.log('🔗 [MemoizedLink] Component rendered:', {
      childrenValue: rest.children?.toString(),
      hasDocument: !!document,
      documentSourceType: document?.source_type,
      documentId: document?.document_id,
      hasImapCallback: !!onImapEmailDetailClick
    });
    const value = rest.children;
    const questionCardProps: QuestionCardProps | undefined =
      question && openQuestion
        ? {
            question: question,
            openQuestion: openQuestion,
          }
        : undefined;

    const documentCardProps: DocumentCardProps | undefined =
      document && updatePresentingDocument
        ? {
            url: document.link,
            icon: document.icon as unknown as React.ReactNode,
            document: document as LoadedOnyxDocument,
            updatePresentingDocument: updatePresentingDocument!,
          }
        : undefined;

    if (value?.toString().startsWith("*")) {
      return (
        <div className="flex-none bg-background-800 inline-block rounded-full h-3 w-3 ml-2" />
      );
    } else if (value?.toString().startsWith("[")) {
      // 检查是否为IMAP邮件，如果是则使用ImapCitation组件
      const isImap = documentCardProps?.document && isImapEmail(documentCardProps.document);
      const hasCallback = !!onImapEmailDetailClick;
      
      console.log('🔎 [MemoizedLink] IMAP email check:', {
        isImap,
        hasCallback,
        documentSourceType: documentCardProps?.document?.source_type,
        documentId: documentCardProps?.document?.document_id,
        documentCardPropsExists: !!documentCardProps
      });
      
      // 如果有IMAP回调函数且有文档，使用ImapCitation（不管source_type）
      if (hasCallback && documentCardProps?.document) {
        console.log('✅ [MemoizedLink] Using ImapCitation component for IMAP email');
        try {
          return (
            <ImapCitation
              document={documentCardProps.document}
              onImapEmailDetailClick={onImapEmailDetailClick}
            >
              {rest.children}
            </ImapCitation>
          );
        } catch (error) {
          console.error('❌ [MemoizedLink] Error rendering ImapCitation:', error);
          // Fallback to regular Citation below
        }
      }
      
      // 对于所有其他情况（包括非IMAP邮件和IMAP邮件但无回调的情况），使用普通Citation
      console.log('🔖 [MemoizedLink] Using regular Citation component');
      return (
        <>
          {documentCardProps ? (
            <Citation document_info={documentCardProps}>
              {rest.children}
            </Citation>
          ) : (
            <Citation question_info={questionCardProps}>
              {rest.children}
            </Citation>
          )}
        </>
      );
    }

    const handleMouseDown = () => {
      let url = href || rest.children?.toString();

      if (url && !url.includes("://")) {
        // Only add https:// if the URL doesn't already have a protocol
        const httpsUrl = `https://${url}`;
        try {
          new URL(httpsUrl);
          url = httpsUrl;
        } catch {
          // If not a valid URL, don't modify original url
        }
      }
      window.open(url, "_blank");
    };
    return (
      <a
        onMouseDown={handleMouseDown}
        className="cursor-pointer text-link hover:text-link-hover"
      >
        {rest.children}
      </a>
    );
  }
);

export const MemoizedParagraph = memo(
  function MemoizedParagraph({ children, fontSize }: any) {
    return (
      <p
        className={`text-neutral-900 dark:text-neutral-200 my-0 ${
          fontSize === "sm" ? "leading-tight text-sm" : ""
        }`}
      >
        {children}
      </p>
    );
  },
  (prevProps, nextProps) => {
    const areEqual = isEqual(prevProps.children, nextProps.children);
    return areEqual;
  }
);

MemoizedAnchor.displayName = "MemoizedAnchor";
MemoizedLink.displayName = "MemoizedLink";
MemoizedParagraph.displayName = "MemoizedParagraph";
