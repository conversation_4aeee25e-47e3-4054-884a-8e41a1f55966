import React, { useState } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { isImapEmail } from '@/lib/email/utils';
import { CompactDocumentCard } from '@/components/search/DocumentDisplay';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ImapCitationProps {
  children: JSX.Element | string | null | React.ReactNode;
  document: OnyxDocument;
  onImapEmailDetailClick?: (document: OnyxDocument) => void;
  index?: number;
  className?: string;
}

export function ImapCitation({
  children,
  document,
  onImapEmailDetailClick,
  index,
  className = '',
}: ImapCitationProps) {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);
  
  console.log('📧 [ImapCitation] Component rendered:', {
    documentId: document.document_id,
    sourceType: document.source_type,
    hasCallback: !!onImapEmailDetailClick,
    childrenValue: children?.toString(),
    index
  });

  // 解析引用文本
  let innerText = '';
  if (index !== undefined) {
    innerText = index.toString();
  }
  if (children) {
    const childrenString = children.toString();
    const childrenSegment1 = childrenString.split('[')[1];
    if (childrenSegment1 !== undefined) {
      const childrenSegment1_0 = childrenSegment1.split(']')[0];
      if (childrenSegment1_0 !== undefined) {
        innerText = childrenSegment1_0;
      }
    }
  }

  // 临时注释掉IMAP类型检查，让所有邮件都能显示【查看邮件详情】按钮
  // if (!isImapEmail(document)) {
  //   return <span className={className}>{children}</span>;
  // }

  const handleTooltipClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔥 [ImapCitation] Tooltip click handler triggered:', {
      documentId: document.document_id,
      hasCallback: !!onImapEmailDetailClick,
      event: e.type
    });
    
    if (onImapEmailDetailClick) {
      try {
        console.log('✅ [ImapCitation] Calling onImapEmailDetailClick callback');
        onImapEmailDetailClick(document);
        console.log('✅ [ImapCitation] Callback executed successfully');
      } catch (error) {
        console.error('❌ [ImapCitation] Error executing callback:', error);
      }
    } else {
      console.warn('⚠️ [ImapCitation] No callback provided!');
    }
    
    setIsTooltipOpen(false);
  };

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip open={isTooltipOpen} onOpenChange={setIsTooltipOpen}>
        <TooltipTrigger asChild>
          <span className={`inline-flex items-center cursor-pointer transition-all duration-200 ease-in-out ${className}`}>
            <span
              className="flex items-center justify-center px-1 h-4 text-[10px] font-medium text-text-700 bg-background-100 rounded-full border border-background-300 hover:bg-background-200 hover:text-text-900 shadow-sm"
              style={{ transform: 'translateY(-10%)', lineHeight: '1' }}
            >
              {innerText}
            </span>
          </span>
        </TooltipTrigger>
        <TooltipContent
          className="border border-neutral-300 hover:text-neutral-900 bg-neutral-100 dark:!bg-[#000] dark:border-neutral-700 cursor-pointer"
          onClick={handleTooltipClick}
          onMouseEnter={() => setIsTooltipOpen(true)}
          onMouseLeave={() => setIsTooltipOpen(false)}
        >
          <div className="space-y-2">
            <CompactDocumentCard
              document={document}
              updatePresentingDocument={() => {}}
              url={document.link}
              icon={null}
            />
            
            {/* IMAP邮件专用的详情按钮 */}
            <div className="pt-2 border-t">
              <button
                onClick={handleTooltipClick}
                className="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
              >
                查看邮件详情
              </button>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}