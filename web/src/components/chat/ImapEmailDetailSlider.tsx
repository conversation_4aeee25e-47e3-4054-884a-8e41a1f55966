import React, { useEffect } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { ImapEmailDetailPanel } from './ImapEmailDetailPanel';

interface ImapEmailDetailSliderProps {
  isOpen: boolean;
  onClose: () => void;
  document: OnyxDocument | null;
  className?: string;
}

export function ImapEmailDetailSlider({ 
  isOpen, 
  onClose, 
  document: emailDocument,
  className 
}: ImapEmailDetailSliderProps) {
  
  // 处理ESC键关闭侧边栏
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* 背景遮罩 - 完全移除，不需要任何遮罩 */}
      
      {/* 侧滑面板 */}
      <div 
        className={`fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-900 shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        } ${className || ''}`}
      >
        {/* 头部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            邮件详情
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors"
            aria-label="关闭邮件详情"
          >
            <svg 
              className="w-5 h-5 text-gray-600 dark:text-gray-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        </div>

        {/* 滚动内容区域 */}
        <div className="flex-1 overflow-y-auto max-h-full">
          {isOpen && emailDocument && (
            <ImapEmailDetailPanel document={emailDocument} />
          )}
        </div>
      </div>
    </>
  );
}