import React, { useState, useEffect, useCallback } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { getDocumentFullContent } from '@/lib/api/document';
import { parseImapEmailContent, formatEmailDate, formatFileSize, ParsedImapEmailContent, EmailAttachment } from '@/lib/email/parser';
import { Spinner } from '@/components/Spinner';

interface ImapEmailDetailPanelProps {
  document: OnyxDocument;
  onRetry?: () => void;
  className?: string;
}

export function ImapEmailDetailPanel({ document, onRetry, className }: ImapEmailDetailPanelProps) {
  const [emailDetail, setEmailDetail] = useState<ParsedImapEmailContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEmailDetailData = useCallback(async () => {
    if (!document) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // 直接使用document_id从现有API获取完整chunk内容
      const fullContent = await getDocumentFullContent(document.document_id, 0);
      
      // 添加详细调试信息
      console.log('🔍 [Debug] Raw email content from API:', fullContent);
      console.log('🔍 [Debug] Content length:', fullContent?.length);
      console.log('🔍 [Debug] Content type:', typeof fullContent);
      console.log('🔍 [Debug] Document info:', {
        documentId: document.document_id,
        sourceType: document.source_type,
        semanticIdentifier: document.semantic_identifier
      });
      
      // 详细分析内容结构 - 重点分析附件信息
      if (fullContent) {
        console.log('📊 [内容分析] 前800字符:', fullContent.substring(0, 800));
        console.log('📊 [内容分析] 完整原始内容长度:', fullContent.length);
        console.log('📊 [内容分析] 是否包含邮件关键字:');
        console.log('  - From/发件人:', fullContent.toLowerCase().includes('from') || fullContent.includes('发件人'));
        console.log('  - To/收件人:', fullContent.toLowerCase().includes('to') || fullContent.includes('收件人'));  
        console.log('  - Date/发送时间:', fullContent.toLowerCase().includes('date') || fullContent.includes('发送时间'));
        console.log('  - Subject:', fullContent.toLowerCase().includes('subject'));
        console.log('  - 附件相关关键字:');
        console.log('    * "附件":', fullContent.includes('附件'));
        console.log('    * "attachment":', fullContent.toLowerCase().includes('attachment'));
        console.log('    * "Content-Type":', fullContent.includes('Content-Type'));
        console.log('    * "Content-Disposition":', fullContent.includes('Content-Disposition'));
        console.log('    * "filename":', fullContent.toLowerCase().includes('filename'));
        console.log('    * ".pptx":', fullContent.includes('.pptx'));
        console.log('    * "产业数字化":', fullContent.includes('产业数字化'));
        console.log('  - @符号:', fullContent.includes('@'));
        
        // 专门搜索附件相关内容
        console.log('📎 [附件专项搜索] 搜索pptx相关内容:');
        const pptxMatches = fullContent.match(/.{0,50}\.pptx.{0,50}/gi);
        if (pptxMatches) {
          pptxMatches.forEach((match, index) => {
            console.log(`  PPTX匹配${index+1}: "${match}"`);
          });
        } else {
          console.log('  未找到.pptx文件引用');
        }
        
        console.log('📎 [附件专项搜索] 搜索产业数字化相关内容:');
        const industryMatches = fullContent.match(/.{0,80}产业数字化.{0,80}/gi);
        if (industryMatches) {
          industryMatches.forEach((match, index) => {
            console.log(`  产业数字化匹配${index+1}: "${match}"`);
          });
        }
        
        console.log('📊 [内容分析] 按行分析前50行:');
        const analysisLines = fullContent.split('\n').slice(0, 50);
        analysisLines.forEach((line, index) => {
          console.log(`  第${index+1}行: "${line.trim()}"`);
        });
      }
      
      // 解析IMAP邮件内容
      let parsedEmail = parseImapEmailContent(fullContent);
      
      console.log('🔍 [Debug] Parsed email result:', parsedEmail);
      
      // 如果解析失败，使用document中的基本信息作为降级处理
      if (!parsedEmail) {
        console.warn('⚠️ [ImapEmailDetailPanel] Email parsing failed, using fallback with document info');
        
        // 从正文内容中提取邮件标题和元信息 - 支持多种格式
        const extractEmailInfo = (content: string) => {
          const lines = (content || '').split('\n');
          let sender = '(未知发件人)';
          let sendDate = '';
          const recipients: string[] = [];
          let ccRecipients: string[] = [];
          const attachments: any[] = [];
          let emailTitle = '';
          let cleanedBody = '';
          let bodyStartIndex = 0;
          
          console.log('🔍 [邮件信息提取] 开始多格式解析...');
          console.log('📝 [邮件信息提取] 原始内容前300字符:', content.substring(0, 300));
          
          // 首先检测邮件格式类型
          const hasOriginalEmailMarker = content.includes('----邮件原文----');
          const hasStructuredHeaders = content.includes('发件人：') && content.includes('收件人：');
          const isSimpleFormat = !hasStructuredHeaders && !hasOriginalEmailMarker;
          
          console.log('📋 [格式检测] 邮件原文标记:', hasOriginalEmailMarker);
          console.log('📋 [格式检测] 结构化邮件头:', hasStructuredHeaders);
          console.log('📋 [格式检测] 简单格式:', isSimpleFormat);
          
          if (hasStructuredHeaders) {
            // 格式1和格式3：结构化邮件头
            if (hasOriginalEmailMarker) {
              console.log('📋 [格式3] 解析带邮件原文标记的结构化格式...');
              
              // 格式3特殊处理：找到----邮件原文----标记后的内容
              const emailOriginalIndex = content.indexOf('----邮件原文----');
              if (emailOriginalIndex !== -1) {
                // 从邮件原文标记后开始解析
                const originalEmailContent = content.substring(emailOriginalIndex + '----邮件原文----'.length);
                const originalLines = originalEmailContent.split('\n');
                
                console.log('📋 [格式3] 邮件原文部分前200字符:', originalEmailContent.substring(0, 200));
                
                // 第一行作为标题（在----邮件原文----之前）
                const beforeOriginalContent = content.substring(0, emailOriginalIndex);
                const beforeLines = beforeOriginalContent.split('\n');
                for (const line of beforeLines) {
                  const trimmed = line.trim();
                  if (trimmed && !trimmed.includes('发件人') && !trimmed.includes('收件人') && trimmed.length > 5) {
                    emailTitle = trimmed;
                    console.log('📧 [格式3-标题] 从邮件原文前找到:', emailTitle);
                    break;
                  }
                }
                
                // 解析----邮件原文----后的结构化信息
                let inHeaderSection = true;
                for (let i = 0; i < originalLines.length; i++) {
                  const line = originalLines[i];
                  if (!line) continue;
                  const trimmedLine = line.trim();
                  
                  console.log(`📋 [格式3-原文第${i+1}行] ${trimmedLine}`);
                  
                  // 发件人匹配
                  if (trimmedLine.includes('发件人：') && sender === '(未知发件人)') {
                    const match = trimmedLine.match(/发件人[：:]\s*"?([^"]+)"?/);
                    if (match && match[1]) {
                      sender = match[1].trim();
                      console.log('✅ [格式3-发件人] 找到:', sender);
                    }
                  }
                  
                  // 收件人匹配
                  if (trimmedLine.includes('收件人：') && recipients.length === 0) {
                    const match = trimmedLine.match(/收件人[：:]\s*(.+?)(?:抄|发送时间|主题|$)/);
                    if (match && match[1]) {
                      const recipientStr = match[1];
                      const recipientMatches = recipientStr.match(/"([^"]+)"/g);
                      if (recipientMatches) {
                        recipients.push(...recipientMatches.map(r => r.replace(/"/g, '')));
                        console.log('✅ [格式3-收件人] 找到:', recipients);
                      }
                      // 处理没有引号的收件人
                      const commaRecipients = recipientStr.split(',').filter(r => r.trim() && !r.includes('"'));
                      if (commaRecipients.length > 0) {
                        recipients.push(...commaRecipients.map(r => r.trim()));
                        console.log('✅ [格式3-收件人-补充] 找到:', commaRecipients);
                      }
                    }
                  }
                  
                  // 抄送匹配
                  if (trimmedLine.includes('抄送') || trimmedLine.includes('抄　送')) {
                    const match = trimmedLine.match(/抄[　\s]*送[：:]\s*(.+?)(?:发送时间|主题|$)/);
                    if (match && match[1]) {
                      const ccStr = match[1];
                      const ccMatches = ccStr.match(/"([^"]+)"/g);
                      if (ccMatches) {
                        ccRecipients = ccMatches.map(r => r.replace(/"/g, ''));
                        console.log('✅ [格式3-抄送] 找到:', ccRecipients);
                      }
                    }
                  }
                  
                  // 发送时间匹配
                  if (trimmedLine.includes('发送时间：') && !sendDate) {
                    const match = trimmedLine.match(/发送时间[：:]\s*(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2})/);
                    if (match && match[1]) {
                      sendDate = match[1];
                      console.log('✅ [格式3-发送时间] 找到:', sendDate);
                    }
                  }
                  
                  // 找到正文开始位置（通常在主题后面或空行后面）
                  if (trimmedLine.includes('主题：') || 
                      (inHeaderSection && trimmedLine.length > 10 && 
                       !trimmedLine.includes('发件人') && !trimmedLine.includes('收件人') && 
                       !trimmedLine.includes('抄送') && !trimmedLine.includes('发送时间'))) {
                    if (trimmedLine.includes('主题：')) {
                      bodyStartIndex = emailOriginalIndex + '----邮件原文----'.length + originalEmailContent.indexOf(line) + line.length;
                    } else {
                      bodyStartIndex = emailOriginalIndex + '----邮件原文----'.length + originalEmailContent.indexOf(line);
                    }
                    inHeaderSection = false;
                    console.log('📋 [格式3] 正文开始位置:', bodyStartIndex);
                    break;
                  }
                }
              }
            } else {
              console.log('📋 [格式1] 解析结构化邮件头...');
              
              for (let i = 0; i < Math.min(lines.length, 20); i++) {
                const line = lines[i];
                if (!line) continue;
                const trimmedLine = line.trim();
              
                // 提取邮件标题 - 第一行通常是标题
                if (i === 0 && trimmedLine && !emailTitle) {
                  emailTitle = trimmedLine;
                  console.log('📧 [格式1-标题] 找到:', emailTitle);
                }
                
                // 发件人匹配
                if (trimmedLine.includes('发件人：')) {
                  const match = trimmedLine.match(/发件人[：:]\s*"?([^"]+)"?/);
                  if (match && match[1]) {
                    sender = match[1].trim();
                    console.log('✅ [格式1-发件人] 找到:', sender);
                    bodyStartIndex = Math.max(bodyStartIndex, i + 1);
                  }
                }
                
                // 收件人匹配
                if (trimmedLine.includes('收件人：')) {
                  const match = trimmedLine.match(/收件人[：:]\s*(.+)/);
                  if (match && match[1]) {
                    const recipientStr = match[1];
                    const recipientMatches = recipientStr.match(/"([^"]+)"/g);
                    if (recipientMatches) {
                      recipients.push(...recipientMatches.map(r => r.replace(/"/g, '')));
                      console.log('✅ [格式1-收件人] 找到:', recipients);
                    }
                    bodyStartIndex = Math.max(bodyStartIndex, i + 1);
                  }
                }
                
                // 抄送匹配
                if (trimmedLine.includes('抄送') || trimmedLine.includes('抄　送')) {
                  const match = trimmedLine.match(/抄[　\s]*送[：:]\s*(.+)/);
                  if (match && match[1]) {
                    const ccStr = match[1];
                    const ccMatches = ccStr.match(/"([^"]+)"/g);
                    if (ccMatches) {
                      ccRecipients = ccMatches.map(r => r.replace(/"/g, ''));
                      console.log('✅ [格式1-抄送] 找到:', ccRecipients);
                    }
                    bodyStartIndex = Math.max(bodyStartIndex, i + 1);
                  }
                }
                
                // 发送时间匹配
                if (trimmedLine.includes('发送时间：')) {
                  const match = trimmedLine.match(/发送时间[：:]\s*(.+)/);
                  if (match && match[1]) {
                    sendDate = match[1].trim();
                    console.log('✅ [格式1-发送时间] 找到:', sendDate);
                    bodyStartIndex = Math.max(bodyStartIndex, i + 1);
                  }
                }
                
                // 检查正文开始
                if (trimmedLine.includes('----邮件原文----') || 
                    trimmedLine.includes('正文：') ||
                    (i > 5 && trimmedLine.length > 50 && !trimmedLine.includes('：'))) {
                  bodyStartIndex = i;
                  break;
                }
              }
            }
          } else {
            // 格式2：简单格式（如问卷填报邮件）
            console.log('📋 [格式2] 解析简单格式邮件...');
            
            // 第一行作为标题
            if (lines[0] && lines[0].trim()) {
              emailTitle = lines[0].trim();
              console.log('📧 [格式2-标题] 找到:', emailTitle);
            }
            
            // 查找时间模式 - 可能在正文中
            for (let i = 0; i < Math.min(lines.length, 30); i++) {
              const line = lines[i];
              if (!line) continue;
              const trimmedLine = line.trim();
              
              console.log(`📋 [格式2-第${i+1}行] ${trimmedLine}`);
              
              // 匹配时间格式 YYYY-MM-DD HH:mm:ss
              const dateMatch = trimmedLine.match(/(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2})/);
              if (dateMatch && dateMatch[1] && !sendDate) {
                sendDate = dateMatch[1];
                console.log('✅ [格式2-时间] 找到:', sendDate);
              }
              
              // 查找邮箱地址作为发件人
              const emailMatch = trimmedLine.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
              if (emailMatch && emailMatch[1] && sender === '(未知发件人)') {
                sender = emailMatch[1];
                console.log('✅ [格式2-发件人] 找到:', sender);
              }
            }
            
            // 简单格式的正文从第二行开始（跳过标题）
            bodyStartIndex = 1;
          }
          
          // 附件信息提取 - 适用于三种格式
          console.log('📎 [附件解析] 开始查找附件信息...');
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (!line) continue;
            const trimmedLine = line.trim();
            
            // 查找附件提及 - "附件为xxx" 或 "附件：xxx"
            if (trimmedLine.includes('附件为') || trimmedLine.includes('attachment')) {
              const attachmentMatch = trimmedLine.match(/附件为(.+?)，/);
              if (attachmentMatch && attachmentMatch[1]) {
                const attachmentName = attachmentMatch[1].trim();
                attachments.push({
                  filename: attachmentName,
                  content_type: 'unknown',
                  size: 0
                });
                console.log('✅ [附件] 从文本找到:', attachmentName);
              }
            }
            
            // 增强的附件文件名提取 - 匹配更多模式
            const patterns = [
              // 标准文件扩展名
              /([^\s《》\[\]]+\.(png|jpg|jpeg|pdf|doc|docx|xls|xlsx|zip|rar|ppt|pptx))/gi,
              // 带中文书名号的文件名
              /《([^》]+\.(xls|xlsx|doc|docx|pdf|ppt|pptx))》/gi,
              // 模板名称格式 - 通常含有部门名称和模板字样
              /([^，。：\s]+部门[^，。：\s]*\.(xls|xlsx|doc|docx))/gi,
              // 报告文件格式
              /([^，。：\s]*报告[^，。：\s]*\.(doc|docx|pdf))/gi
            ];
            
            patterns.forEach(pattern => {
              const matches = trimmedLine.match(pattern);
              if (matches) {
                matches.forEach(match => {
                  // 清理匹配的文件名
                  let cleanedName = match;
                  if (match.startsWith('《') && match.endsWith('》')) {
                    cleanedName = match.slice(1, -1); // 移除书名号
                  }
                  
                  if (!attachments.some(a => a.filename === cleanedName)) {
                    const extension = cleanedName.split('.').pop()?.toLowerCase();
                    let contentType = 'unknown';
                    if (['xls', 'xlsx'].includes(extension || '')) contentType = 'application/vnd.ms-excel';
                    else if (['doc', 'docx'].includes(extension || '')) contentType = 'application/msword';
                    else if (extension === 'pdf') contentType = 'application/pdf';
                    else if (['png', 'jpg', 'jpeg'].includes(extension || '')) contentType = 'image/' + extension;
                    
                    attachments.push({
                      filename: cleanedName,
                      content_type: contentType,
                      size: 0
                    });
                    console.log('✅ [附件] 从模式匹配找到:', cleanedName);
                  }
                });
              }
            });
          }
          
          // 提取纯正文内容 - 根据不同格式处理
          if (hasOriginalEmailMarker) {
            // 格式3：特殊处理，避免重复显示
            console.log('📝 [格式3-正文提取] 处理邮件原文标记格式...');
            
            const firstOriginalIndex = content.indexOf('----邮件原文----');
            const secondOriginalIndex = content.indexOf('----邮件原文----', firstOriginalIndex + 1);
            
            if (secondOriginalIndex !== -1) {
              // 如果有两个----邮件原文----，取第二个后面的内容作为正文
              const actualBodyContent = content.substring(secondOriginalIndex + '----邮件原文----'.length);
              const bodyContentLines = actualBodyContent.split('\n');
              
              // 跳过邮件头信息，找到实际正文
              const cleanBodyLines = [];
              let actualBodyStart = false;
              for (let i = 0; i < bodyContentLines.length; i++) {
                const line = bodyContentLines[i];
                if (!line) continue;
                const trimmed = line.trim();
                
                // 跳过邮件头信息
                if (trimmed.includes('发件人：') || trimmed.includes('收件人：') || 
                    trimmed.includes('抄送') || trimmed.includes('发送时间：') || 
                    trimmed.includes('主题：') || trimmed === '') {
                  continue;
                }
                
                // 找到实际正文开始
                if (!actualBodyStart && trimmed.length > 0) {
                  actualBodyStart = true;
                }
                
                if (actualBodyStart) {
                  cleanBodyLines.push(line);
                }
              }
              
              cleanedBody = cleanBodyLines.join('\n').trim();
              console.log('📝 [格式3-正文提取] 从第二个邮件原文标记后提取，长度:', cleanedBody.length);
            } else {
              // 只有一个----邮件原文----，取第一个----邮件原文----前的内容作为正文
              cleanedBody = content.substring(0, firstOriginalIndex).trim();
              console.log('📝 [格式3-正文提取] 从邮件原文标记前提取，长度:', cleanedBody.length);
            }
          } else {
            // 格式1和格式2：原有逻辑
            console.log('📝 [格式1/2-正文提取] 使用标准逻辑...');
            const bodyLines = lines.slice(bodyStartIndex);
            const cleanBodyLines = [];
            let foundStart = false;
            
            for (const line of bodyLines) {
              const trimmed = line.trim();
              
              // 跳过邮件头信息行
              if (trimmed.includes('发件人：') || trimmed.includes('收件人：') || 
                  trimmed.includes('抄送:') || trimmed.includes('抄　送:') || 
                  trimmed.includes('发送时间：') || trimmed === '') {
                continue;
              }
              
              // 找到正文开始
              if (!foundStart && trimmed.length > 0) {
                foundStart = true;
              }
              
              if (foundStart) {
                cleanBodyLines.push(line);
              }
            }
            
            cleanedBody = cleanBodyLines.join('\n').trim();
          }
          
          console.log('🎯 [最终提取结果]:', { 
            emailTitle, 
            sender, 
            sendDate, 
            recipients, 
            ccRecipients,
            attachments: attachments.length,
            cleanedBodyLength: cleanedBody.length 
          });
          return { sender, sendDate, recipients, ccRecipients, emailTitle, cleanedBody, attachments };
        };
        
        const emailInfo = extractEmailInfo(fullContent);
        
        // 如果第一种方法失败，尝试更简单的邮件地址提取
        let finalSender = emailInfo.sender;
        let finalRecipients = emailInfo.recipients;
        let finalSendDate = emailInfo.sendDate;
        
        if (finalSender === '(未知发件人)' && fullContent && fullContent.includes('@')) {
          console.log('📧 [备用策略] 尝试简单邮件地址提取...');
          const emailRegex = /[\w.-]+@[\w.-]+\.\w+/g;
          const allEmails = fullContent.match(emailRegex);
          if (allEmails && allEmails.length > 0) {
            finalSender = allEmails[0];
            if (allEmails.length > 1) {
              finalRecipients = [...finalRecipients, ...allEmails.slice(1)];
            }
            console.log('✅ [备用策略] 提取到邮箱地址:', allEmails);
          }
        }
        
        // 如果还是没有时间，尝试提取任何时间格式
        if (!finalSendDate && fullContent) {
          console.log('📅 [备用策略] 尝试时间格式提取...');
          const datePatterns = [
            /\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}/,
            /\d{4}年\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{1,2}/,
            /\d{1,2}[-/]\d{1,2}[-/]\d{4}\s+\d{1,2}:\d{1,2}/
          ];
          
          for (const pattern of datePatterns) {
            const match = fullContent.match(pattern);
            if (match && match[0]) {
              finalSendDate = match[0];
              console.log('✅ [备用策略] 找到时间:', finalSendDate);
              break;
            }
          }
        }
        
        parsedEmail = {
          subject: emailInfo.emailTitle || document.semantic_identifier || '(无主题)',
          sender: finalSender, 
          recipients: finalRecipients,
          sendDate: finalSendDate,
          bodyText: emailInfo.cleanedBody || fullContent || '(无内容)',
          // 可选字段
          ccRecipients: emailInfo.ccRecipients || undefined,
          bccRecipients: undefined,
          attachments: emailInfo.attachments && emailInfo.attachments.length > 0 ? emailInfo.attachments : undefined
        };
        
        console.log('✅ [ImapEmailDetailPanel] Using fallback email data:', parsedEmail);
      }
      
      setEmailDetail(parsedEmail);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取邮件详情失败';
      setError(errorMessage);
      console.error('获取IMAP邮件详情失败:', err);
    } finally {
      setLoading(false);
    }
  }, [document?.document_id]);

  useEffect(() => {
    fetchEmailDetailData();
  }, [fetchEmailDetailData]);

  const handleRetry = () => {
    fetchEmailDetailData();
    if (onRetry) {
      onRetry();
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <Spinner />
        <span className="ml-2">正在获取邮件详情...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-red-600 mb-4">
          错误: {error}
        </div>
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  if (!emailDetail) {
    return <div className="p-4">暂无邮件详情</div>;
  }

  return (
    <div className="p-4 space-y-6">
      {/* 邮件标题 */}
      <div>
        <h3 className="text-xl font-bold mb-2">{emailDetail.subject}</h3>
      </div>

      {/* 邮件基本信息 */}
      <div className="space-y-3">
        <div>
          <span className="font-medium text-gray-600">发件人：</span>
          <span className="ml-2">{emailDetail.sender}</span>
        </div>
        
        <div>
          <span className="font-medium text-gray-600">收件人：</span>
          <span className="ml-2">{emailDetail.recipients.length > 0 ? emailDetail.recipients.join(', ') : '(未知收件人)'}</span>
        </div>
        
        {emailDetail.ccRecipients && emailDetail.ccRecipients.length > 0 && (
          <div>
            <span className="font-medium text-gray-600">抄送：</span>
            <span className="ml-2">{emailDetail.ccRecipients.join(', ')}</span>
          </div>
        )}
        
        {emailDetail.bccRecipients && emailDetail.bccRecipients.length > 0 && (
          <div>
            <span className="font-medium text-gray-600">密送：</span>
            <span className="ml-2">{emailDetail.bccRecipients.join(', ')}</span>
          </div>
        )}
        
        <div>
          <span className="font-medium text-gray-600">发送时间：</span>
          <span className="ml-2">{formatEmailDate(emailDetail.sendDate)}</span>
        </div>
      </div>

      {/* 邮件正文 */}
      <div>
        <h4 className="font-medium text-gray-600 mb-2">正文：</h4>
        <div className="max-w-none border rounded p-4 bg-gray-50 border-gray-200 dark:border-gray-600 dark:bg-gray-100 max-h-96 overflow-y-auto">
          <pre className="whitespace-pre-wrap font-sans text-sm text-gray-800 dark:text-gray-900 leading-relaxed">
            {emailDetail.bodyText}
          </pre>
        </div>
      </div>

      {/* 附件列表 - 始终显示，即使为空 */}
      <div>
        <h4 className="font-medium text-gray-600 mb-2">附件：</h4>
        {emailDetail.attachments && emailDetail.attachments.length > 0 ? (
          <div className="space-y-2">
            {emailDetail.attachments.map((attachment: EmailAttachment, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center space-x-3">
                  <span className="text-gray-600">📎</span>
                  <div>
                    <div className="font-medium">{attachment.filename}</div>
                    <div className="text-sm text-gray-500">
                      {attachment.content_type} • {formatFileSize(attachment.size)}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  附件暂不支持下载
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-gray-500 p-2 border rounded bg-gray-50">
            此邮件无附件
          </div>
        )}
      </div>
    </div>
  );
}