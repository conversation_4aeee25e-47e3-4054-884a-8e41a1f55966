#!/usr/bin/env python3
"""
修复Celery Beat调度器问题的脚本
解决celerybeat-schedule文件损坏导致的任务调度失败
"""

import subprocess
import time
import sys

def run_command(cmd, timeout=30):
    """执行命令并返回结果"""
    try:
        print(f"🔧 执行命令: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"❌ 失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print(f"❌ 命令超时")
        return False, "", "Command timeout"
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False, "", str(e)

def check_docker_status():
    """检查Docker状态"""
    print("🔍 检查Docker容器状态...")
    success, stdout, stderr = run_command("docker ps --filter name=km-background --format 'table {{.Names}}\t{{.Status}}'")
    if success and "km-background" in stdout:
        print("✅ km-background容器正在运行")
        return True
    else:
        print("❌ km-background容器未运行")
        return False

def stop_container():
    """停止容器"""
    print("\n🛑 停止km-background容器...")
    success, _, _ = run_command("docker stop km-background")
    if success:
        print("✅ 容器已停止")
        time.sleep(5)
        return True
    else:
        print("❌ 停止容器失败")
        return False

def clean_schedule_files():
    """清理损坏的调度文件"""
    print("\n🧹 清理损坏的调度文件...")
    
    # 启动容器以便清理文件
    print("启动容器进行文件清理...")
    run_command("docker start km-background")
    time.sleep(10)
    
    # 清理celerybeat-schedule文件
    commands = [
        "docker exec km-background find /app -name 'celerybeat-schedule' -delete",
        "docker exec km-background find /app -name '*.db' -path '*/celery*' -delete",
        "docker exec km-background rm -f /app/celerybeat-schedule*"
    ]
    
    for cmd in commands:
        success, _, _ = run_command(cmd)
        if success:
            print(f"✅ 清理命令执行成功")
        else:
            print(f"⚠️ 清理命令执行失败（可能文件不存在）")

def restart_container():
    """重启容器"""
    print("\n🔄 重启km-background容器...")
    success, _, _ = run_command("docker restart km-background")
    if success:
        print("✅ 容器重启成功")
        print("⏳ 等待服务启动...")
        time.sleep(20)
        return True
    else:
        print("❌ 容器重启失败")
        return False

def verify_celery_beat():
    """验证Celery Beat是否正常启动"""
    print("\n✅ 验证Celery Beat状态...")
    
    # 检查Beat启动日志
    print("检查Beat启动日志...")
    success, stdout, _ = run_command("docker logs --tail 50 km-background 2>&1 | grep -E '(celery beat.*starting|scheduler.*ready|beat.*starting)'")
    if success and stdout.strip():
        print("✅ 找到Beat启动日志:")
        print(stdout)
    else:
        print("⚠️ 未找到Beat启动日志，检查错误日志...")
        run_command("docker logs --tail 20 km-background 2>&1 | grep -E '(ERROR|error.*schedule|gdbm.error)'")

def verify_task_scheduling():
    """验证任务调度是否正常"""
    print("\n📋 验证任务调度...")
    
    # 等待一段时间让调度器工作
    print("等待调度器开始工作...")
    time.sleep(30)
    
    # 检查check_for_indexing任务
    print("检查check_for_indexing任务调度...")
    success, stdout, _ = run_command("docker logs --tail 100 km-background 2>&1 | grep 'check_for_indexing'")
    if success and stdout.strip():
        print("✅ 找到任务调度日志:")
        print(stdout[-500:])  # 显示最后500字符
    else:
        print("⚠️ 未找到任务调度日志")

def monitor_heartbeat():
    """监控心跳日志"""
    print("\n💓 监控心跳日志...")
    print("检查是否有心跳相关日志...")
    
    success, stdout, _ = run_command("docker logs --tail 50 km-background 2>&1 | grep -E '(🔥|HEARTBEAT|DOCFETCHING|DOCPROCESSING)'")
    if success and stdout.strip():
        print("✅ 找到心跳相关日志:")
        print(stdout)
    else:
        print("⚠️ 暂时未找到心跳日志，这是正常的，需要等待任务被调度")

def main():
    """主函数"""
    print("🔧 Celery Beat调度器修复脚本")
    print("=" * 50)
    
    # 检查Docker状态
    if not check_docker_status():
        print("❌ 请确保Docker正在运行")
        return False
    
    # 停止容器
    if not stop_container():
        print("❌ 无法停止容器，尝试强制重启")
    
    # 清理调度文件
    clean_schedule_files()
    
    # 重启容器
    if not restart_container():
        print("❌ 容器重启失败")
        return False
    
    # 验证修复
    verify_celery_beat()
    verify_task_scheduling()
    monitor_heartbeat()
    
    print("\n" + "=" * 50)
    print("🎯 修复完成！")
    print("\n📋 后续监控命令:")
    print("1. 实时监控Beat日志:")
    print("   docker logs -f km-background 2>&1 | grep -E '(beat|schedule|check_for_indexing)'")
    print("\n2. 实时监控心跳日志:")
    print("   docker logs -f km-background 2>&1 | grep -E '(🔥|HEARTBEAT|DOCFETCHING|DOCPROCESSING)'")
    print("\n3. 检查IndexAttempt状态:")
    print("   docker exec km-background python3 -c \"from onyx.db.engine.sql_engine import get_session_with_current_tenant; from onyx.db.models import IndexAttempt; from sqlalchemy import select, desc; [print(f'ID: {a.id}, Status: {a.status}, Counter: {a.heartbeat_counter}') for a in get_session_with_current_tenant().__enter__().execute(select(IndexAttempt).order_by(desc(IndexAttempt.time_created)).limit(3)).scalars().all()]\"")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
