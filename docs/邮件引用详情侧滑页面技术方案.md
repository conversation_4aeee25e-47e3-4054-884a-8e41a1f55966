# 邮件引用详情侧滑页面技术方案

## 📋 需求概述

### 当前功能
- 在聊天回答中，D1、D2等引用链接鼠标悬浮时显示简单的悬浮框
- 悬浮框只显示邮件标题和一行简介信息
- 点击悬浮框可以在新标签页打开邮件链接

### 改造需求
1. **保持现有悬浮框功能**：鼠标悬浮D1、D2等引用时，仍显示原有的简单悬浮框
2. **增强悬浮框交互**：鼠标移动到悬浮框上时，悬浮框保持显示且可点击
3. **侧滑详情页面**：点击悬浮框后，对话框右侧向左滑出详细页面
4. **完整邮件信息**：详情页面显示邮件标题、发件人、发件时间、收件人、正文、附件文档全文
5. **附件下载功能**：附件文档可以点击下载

## 🔍 现有实现分析

### 引用悬浮框实现链路

#### 1. 引用解析层
**文件**: `web/src/app/chat/message/MemoizedTextComponents.tsx`
- **核心组件**: `MemoizedAnchor`
- **功能**: 解析 `[D1]`, `[D2]` 等引用模式（正则：`/\[(D|Q)?(\d+)\]/`）
- **关键代码**:
```tsx
const match = value.match(/\[(D|Q)?(\d+)\]/);
const index = parseInt(match_item, 10) - 1;
const associatedDoc = isDocument ? docs?.[index] : null;
```

#### 2. 引用渲染层
**文件**: `web/src/components/search/results/Citation.tsx`
- **核心组件**: `Citation`
- **功能**: 使用Tooltip包装引用，显示悬浮框
- **关键代码**:
```tsx
<TooltipTrigger asChild>
  <span onClick={() => openDocument(document, updatePresentingDocument)}>
    {innerText}
  </span>
</TooltipTrigger>
<TooltipContent>
  <CompactDocumentCard document={document} />
</TooltipContent>
```

#### 3. 悬浮框内容层
**文件**: `web/src/components/search/DocumentDisplay.tsx`
- **核心组件**: `CompactDocumentCard`
- **功能**: 显示文档标题、简介、更新时间
- **当前限制**: 只显示基本信息，缺少邮件详细内容

#### 4. 文档点击处理
**文件**: `web/src/lib/search/utils.ts`
- **核心函数**: `openDocument`
- **当前逻辑**: 
  - 有链接则在新标签页打开
  - 文件类型则调用 `updatePresentingDocument`

### 数据结构分析

#### OnyxDocument 接口
**文件**: `web/src/lib/search/interfaces.ts`
```typescript
interface OnyxDocument {
  document_id: string;
  semantic_identifier: string | null;
  link: string;
  source_type: ValidSources;
  blurb: string;
  metadata: { [key: string]: string };
  updated_at: string | null;
  // ...其他字段
}
```

#### 🚨 **重要发现：IMAP与Gmail数据存储结构差异！**

**经过深入分析IMAP Connector代码发现重大差异：**

##### IMAP邮件数据结构（`backend/onyx/connectors/imap/connector.py:505-514`）：
```python
return Document(
    id=email_headers.id,
    title=email_headers.subject, 
    semantic_identifier=email_headers.subject,
    metadata={},  # 空的metadata！
    source=DocumentSource.IMAP,
    sections=[TextSection(text=email_body)],  # 只有邮件正文
    primary_owners=primary_owners,
    external_access=external_access,
)
```

##### Gmail邮件数据结构（`backend/onyx/connectors/gmail/connector.py:147-149`）：
```python
message_data = ""
for name, value in metadata.items():
    if name != "updated_at":
        message_data += f"{name}: {value}\n"
return TextSection(text=message_body_text + message_data)  # 正文+邮件头信息
```

##### 关键差异：
- **IMAP**: 邮件头信息（发件人、收件人等）存储在 `EmailHeaders` 对象中，但只在内存中处理，**未写入Vespa的content字段**
- **Gmail**: 邮件头信息追加到 `message_body_text` 后，完整存储在Vespa的content字段中
- **IMAP**: `metadata={}` 为空，意味着无法从chunk内容解析出发件人、收件人等信息

##### 解决方案：
**必须开发后端API！** 由于IMAP connector没有将邮件头信息存储到Vespa中，我们需要从原始IMAP源重新获取完整邮件信息。

## 🎯 技术方案设计

### 方案概述
基于对引用处理系统(`process_message.py`)的深度分析，确定**IMAP Connector改造**的优化方案：

1. **改造IMAP Connector**：在数据索引时存储完整邮件信息到Vespa
2. **复用现有API**：直接使用`/api/document/chunk-info` API获取完整内容
3. **前端解析增强**：直接解析Vespa中的完整邮件信息，无需新建API
4. **保持向后兼容**：不影响现有引用悬浮框功能
5. **增强用户体验**：新增侧滑页面显示完整邮件详情

### 🔍 **关键技术发现**
通过分析`_post_llm_answer_processing`方法，确认了引用系统的完整工作流程：

1. **引用映射机制**：LLM生成`CitationInfo{citation_num, document_id}` → `_translate_citations()` → `MessageSpecificCitations{citation_num → db_search_doc.id}`
2. **数据获取路径**：前端通过`document_id`使用`/api/document/chunk-info`直接从 Vespa 获取完整chunk内容
3. **API复用确认**：现有`chunk-info` API完全满足需求，无需新建API

### 架构设计

基于对`process_message.py`中引用处理系统的深度分析，完整的数据流程如下：

```
引用生成和映射流程：
LLM生成回答 → CitationInfo{citation_num, document_id} → _translate_citations()
                                ↓
                    MessageSpecificCitations{citation_num → db_search_doc.id}
                                ↓
                        前端通过document_id获取OnyxDocument对象

现有引用显示流程：
[D1] → MemoizedAnchor → Citation → CompactDocumentCard (悬浮框)
          ↓                              ↓ (点击)
      获取docs[index]              openDocument (新标签页)

IMAP邮件改造后的增强流程：
[D1] → MemoizedAnchor → Citation → ImapCitation (IMAP专用增强悬浮框)
          ↓                              ↓ (点击，检测source_type=imap)
      获取docs[index]            showImapEmailDetailSlider (侧滑页面)
      (OnyxDocument)                     ↓
          ↓                    ImapEmailDetailPanel (详情组件)
   document_id获取                       ↓
          ↓              /api/document/chunk-info?document_id={document_id}&chunk_id=0
    存储完整邮件信息                      ↓
      (改造后)                  返回完整邮件信息的chunk内容
                                        ↓
                            parseImapEmailContent() (前端解析邮件信息)
                                        ↓
                                显示完整邮件详情页面
```

## 🔧 详细实施方案

### 第一阶段：后端IMAP Connector改造（核心）

#### 1.1 扩展EmailHeaders模型
**文件**: `backend/onyx/connectors/imap/models.py`

```python
class EmailHeaders(BaseModel):
    """
    Model for email headers extracted from IMAP messages.
    扩展以支持完整邮件信息
    """
    id: str
    subject: str
    sender: str
    recipients: str | None
    cc_recipients: str | None      # 新增：抄送
    bcc_recipients: str | None     # 新增：密送
    date: datetime

    @classmethod
    def from_email_msg(cls, email_msg: Message) -> "EmailHeaders":
        def _decode(header: str, default: str | None = None) -> str | None:
            # 现有解码逻辑...
            
        # 现有字段获取...
        message_id = _decode(header=Header.MESSAGE_ID_HEADER)
        subject = _decode(header=Header.SUBJECT_HEADER) or "Unknown Subject"
        from_ = _decode(header=Header.FROM_HEADER)
        to = _decode(header=Header.TO_HEADER)
        if not to:
            to = _decode(header=Header.DELIVERED_TO_HEADER)
            
        # 新增：获取CC和BCC
        cc = _decode(header="Cc")
        bcc = _decode(header="Bcc")
        
        date_str = _decode(header=Header.DATE_HEADER)
        date = _parse_date(date_str=date_str)

        return cls.model_validate({
            "id": message_id,
            "subject": subject,
            "sender": from_,
            "recipients": to,
            "cc_recipients": cc,      # 新增
            "bcc_recipients": bcc,    # 新增
            "date": date,
        })
```

#### 1.2 重写邮件内容和附件解析函数
**文件**: `backend/onyx/connectors/imap/connector.py`

```python
def _parse_email_content_and_attachments(
    email_msg: Message,
    email_headers: EmailHeaders,
) -> tuple[str, list[dict]]:
    """
    解析邮件正文和附件信息
    返回: (邮件正文, 附件列表)
    """
    body_text = ""
    attachments = []
    
    for part in email_msg.walk():
        if part.is_multipart():
            continue
            
        content_disposition = part.get_content_disposition()
        content_type = part.get_content_type()
        
        if content_disposition == 'attachment':
            # 处理附件
            filename = part.get_filename()
            if filename:
                attachment_size = len(part.get_payload(decode=True)) if part.get_payload(decode=True) else 0
                attachments.append({
                    'filename': filename,
                    'content_type': content_type,
                    'size': attachment_size
                })
        elif content_type.startswith('text/'):
            # 处理文本内容
            if not body_text:  # 只取第一个文本部分作为主要正文
                try:
                    charset = part.get_content_charset() or "utf-8"
                    raw_payload = part.get_payload(decode=True)
                    if isinstance(raw_payload, bytes):
                        text = raw_payload.decode(charset)
                        # 如果是HTML，使用BeautifulSoup清理
                        if content_type == 'text/html':
                            soup = bs4.BeautifulSoup(text, features="html.parser")
                            body_text = " ".join(str_section for str_section in soup.stripped_strings)
                        else:
                            body_text = text
                except Exception as e:
                    logger.warn(f"Failed to decode email body: {e}")
    
    return body_text, attachments
```

#### 1.3 修改文档生成函数存储完整信息
**文件**: `backend/onyx/connectors/imap/connector.py`

```python
def _convert_email_headers_and_body_into_document(
    email_msg: Message,
    email_headers: EmailHeaders,
    include_perm_sync: bool,
) -> Document:
    """
    改造：将完整邮件信息存储到Vespa，类似Gmail connector
    """
    # 解析邮件正文和附件
    email_body, attachments = _parse_email_content_and_attachments(email_msg, email_headers)
    
    # 构建完整的邮件元数据信息（类似Gmail connector格式）
    message_data = f"""
from: {email_headers.sender}
to: {email_headers.recipients or ''}"""
    
    # 添加抄送信息
    if email_headers.cc_recipients:
        message_data += f"\ncc: {email_headers.cc_recipients}"
    
    # 添加密送信息  
    if email_headers.bcc_recipients:
        message_data += f"\nbcc: {email_headers.bcc_recipients}"
    
    message_data += f"""
subject: {email_headers.subject}
date: {email_headers.date.isoformat()}"""
    
    # 如果有附件，添加附件信息
    if attachments:
        message_data += f"\nattachments: {len(attachments)} files"
        for att in attachments:
            message_data += f"\n- {att['filename']} ({att['content_type']}, {att['size']} bytes)"
    
    # 关键改造：将正文和邮件头信息合并存储（类似Gmail connector）
    full_content = email_body + "\n" + message_data
    
    # 其他处理逻辑保持不变...
    sender_name, sender_addr = _parse_singular_addr(raw_header=email_headers.sender)
    parsed_recipients = (
        _parse_addrs(raw_header=email_headers.recipients)
        if email_headers.recipients
        else []
    )

    expert_info_map = {
        recipient_addr: BasicExpertInfo(
            display_name=recipient_name, email=recipient_addr
        )
        for recipient_name, recipient_addr in parsed_recipients
    }
    if sender_addr not in expert_info_map:
        expert_info_map[sender_addr] = BasicExpertInfo(
            display_name=sender_name, email=sender_addr
        )

    primary_owners = list(expert_info_map.values())
    external_access = (
        ExternalAccess(
            external_user_emails=set(expert_info_map.keys()),
            external_user_group_ids=set(),
            is_public=False,
        )
        if include_perm_sync
        else None
    )

    return Document(
        id=email_headers.id,
        title=email_headers.subject,
        semantic_identifier=email_headers.subject,
        metadata={},
        source=DocumentSource.IMAP,
        sections=[TextSection(text=full_content)],  # 存储完整信息！
        primary_owners=primary_owners,
        external_access=external_access,
    )
```

### 第二阶段：前端IMAP邮件解析和组件开发

#### 2.1 获取文档完整内容的API工具函数
**文件**: `web/src/lib/api/document.ts` (新建或扩展)

```typescript
/**
 * 获取文档的完整chunk内容 - 基于现有API的封装
 */
export async function getDocumentFullContent(
  documentId: string, 
  chunkId: number = 0
): Promise<string> {
  const response = await fetch(
    `/api/document/chunk-info?document_id=${encodeURIComponent(documentId)}&chunk_id=${chunkId}`
  );
  
  if (!response.ok) {
    throw new Error(`Failed to fetch document content: ${response.statusText}`);
  }
  
  const chunkInfo = await response.json();
  return chunkInfo.content;
}
```

#### 2.2 IMAP邮件内容解析器
**文件**: `web/src/lib/email/parser.ts` (新建)

```typescript
interface ParsedImapEmailContent {
  subject: string;
  sender: string;
  recipients: string[];
  ccRecipients?: string[];
  bccRecipients?: string[];
  sendDate: string;
  bodyText: string;
  attachments?: EmailAttachment[];
}

interface EmailAttachment {
  filename: string;
  content_type: string;
  size: number;
}

export function parseImapEmailContent(chunkContent: string): ParsedImapEmailContent | null {
  /**
   * 解析IMAP邮件chunk内容，格式为：
   * [邮件正文]
   * from: <EMAIL>
   * to: <EMAIL>
   * cc: <EMAIL>
   * subject: 邮件主题
   * date: 2024-01-01T10:00:00
   * attachments: 2 files
   * - file1.pdf (application/pdf, 1024 bytes)
   * - file2.docx (application/vnd.openxmlformats-officedocument.wordprocessingml.document, 2048 bytes)
   */
  
  const lines = chunkContent.split('\n');
  const emailFields: Record<string, string> = {};
  const attachments: EmailAttachment[] = [];
  let bodyEndIndex = -1;
  let attachmentSection = false;
  
  // 从后往前解析邮件字段和附件信息
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i].trim();
    
    // 检测附件列表
    if (line.startsWith('- ') && attachmentSection) {
      const attachmentMatch = line.match(/^- (.+?) \((.+?), (\d+) bytes\)$/);
      if (attachmentMatch) {
        attachments.unshift({
          filename: attachmentMatch[1],
          content_type: attachmentMatch[2],
          size: parseInt(attachmentMatch[3], 10)
        });
      }
      continue;
    }
    
    // 检测附件标题行
    if (line.startsWith('attachments: ')) {
      attachmentSection = true;
      emailFields['attachments'] = line.split(': ')[1];
      if (bodyEndIndex === -1) {
        bodyEndIndex = i;
      }
      continue;
    }
    
    // 解析邮件字段
    if (line.includes(': ')) {
      const [key, ...valueParts] = line.split(': ');
      const value = valueParts.join(': ');
      
      if (['from', 'to', 'cc', 'bcc', 'subject', 'date'].includes(key.toLowerCase())) {
        emailFields[key.toLowerCase()] = value;
        attachmentSection = false;
        if (bodyEndIndex === -1) {
          bodyEndIndex = i;
        }
      }
    }
  }
  
  // 提取正文（邮件字段之前的内容）
  const bodyText = bodyEndIndex > 0 
    ? lines.slice(0, bodyEndIndex).join('\n').trim()
    : chunkContent;
  
  if (!emailFields.from && !emailFields.subject) {
    return null; // 不是邮件内容
  }
  
  return {
    subject: emailFields.subject || '(无主题)',
    sender: emailFields.from || '',
    recipients: emailFields.to ? emailFields.to.split(', ').map(s => s.trim()) : [],
    ccRecipients: emailFields.cc ? emailFields.cc.split(', ').map(s => s.trim()) : undefined,
    bccRecipients: emailFields.bcc ? emailFields.bcc.split(', ').map(s => s.trim()) : undefined,
    sendDate: emailFields.date || '',
    bodyText: bodyText,
    attachments: attachments.length > 0 ? attachments : undefined,
  };
}

export function formatEmailDate(dateStr: string): string {
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateStr;
  }
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
```

#### 2.2 IMAP邮件检测器
**文件**: `web/src/lib/email/utils.ts` (新建)

```typescript
import { OnyxDocument } from '@/lib/search/interfaces';

export function isImapEmail(document: OnyxDocument): boolean {
  return document.source_type === 'imap';
}

export function getImapMessageId(document: OnyxDocument): string | null {
  // IMAP: message_id存储在document.document_id中
  if (isImapEmail(document)) {
    return document.document_id;
  }
  return null;
}
```

### 第二阶段：前端侧滑组件

#### 2.1 侧滑容器组件
**文件**: `web/src/components/chat/EmailDetailSlider.tsx` (新建)

```tsx
import React, { useState, useEffect } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { EmailDetailPanel } from './EmailDetailPanel';

interface EmailDetailSliderProps {
  isOpen: boolean;
  onClose: () => void;
  document: OnyxDocument | null;
}

export function EmailDetailSlider({ 
  isOpen, 
  onClose, 
  document 
}: EmailDetailSliderProps) {
  return (
    <div
      className={`
        fixed top-0 right-0 h-full z-50 bg-white dark:bg-gray-900 
        shadow-2xl transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        w-[500px] border-l border-gray-200 dark:border-gray-700
      `}
    >
      <div className="flex flex-col h-full">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">邮件详情</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            ✕
          </button>
        </div>
        
        {/* 内容区 */}
        <div className="flex-1 overflow-y-auto">
          {document && (
            <EmailDetailPanel document={document} />
          )}
        </div>
      </div>
    </div>
  );
}
```

#### 2.2 邮件详情面板组件
**文件**: `web/src/components/chat/EmailDetailPanel.tsx` (新建)

```tsx
import React, { useState, useEffect } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { EmailDetailResponse } from '@/lib/types/email';
import { Spinner } from '@/components/Spinner';

interface EmailDetailPanelProps {
  document: OnyxDocument;
}

export function EmailDetailPanel({ document }: EmailDetailPanelProps) {
  const [emailDetail, setEmailDetail] = useState<ParsedEmailContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchEmailDetail();
  }, [document.document_id]);

  const fetchEmailDetail = async () => {
    try {
      setLoading(true);
      // 使用现有的chunk-info API获取完整chunk内容
      const fullContent = await getDocumentFullContent(document.document_id, 0);
      
      // 解析IMAP邮件内容（改造IMAP connector后将包含完整邮件信息）
      const parsedEmail = parseImapEmailContent(fullContent);
      if (!parsedEmail) {
        throw new Error('无法解析IMAP邮件内容，可能数据格式不正确');
      }
      
      setEmailDetail(parsedEmail);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取邮件详情失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-600">
        错误: {error}
      </div>
    );
  }

  if (!emailDetail) {
    return <div className="p-4">暂无邮件详情</div>;
  }

  return (
    <div className="p-4 space-y-6">
      {/* 邮件标题 */}
      <div>
        <h3 className="text-xl font-bold mb-2">{emailDetail.subject}</h3>
      </div>

      {/* 邮件基本信息 */}
      <div className="space-y-3">
        <div>
          <span className="font-medium text-gray-600">发件人：</span>
          <span className="ml-2">{emailDetail.sender}</span>
        </div>
        
        <div>
          <span className="font-medium text-gray-600">收件人：</span>
          <span className="ml-2">{emailDetail.recipients.join(', ')}</span>
        </div>
        
        {emailDetail.cc_recipients && emailDetail.cc_recipients.length > 0 && (
          <div>
            <span className="font-medium text-gray-600">抄送：</span>
            <span className="ml-2">{emailDetail.cc_recipients.join(', ')}</span>
          </div>
        )}
        
        <div>
          <span className="font-medium text-gray-600">发送时间：</span>
          <span className="ml-2">{formatEmailDate(emailDetail.sendDate)}</span>
        </div>
      </div>

      {/* 邮件标签 */}
      {emailDetail.labels && emailDetail.labels.length > 0 && (
        <div>
          <span className="font-medium text-gray-600">标签：</span>
          <div className="flex flex-wrap gap-1 mt-1">
            {emailDetail.labels.map((label, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {label}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* 邮件正文 */}
      <div>
        <h4 className="font-medium text-gray-600 mb-2">正文：</h4>
        <div className="prose max-w-none border rounded p-4 bg-gray-50 dark:bg-gray-800">
          <pre className="whitespace-pre-wrap font-sans text-sm">
            {emailDetail.bodyText}
          </pre>
        </div>
      </div>

      {/* 附件提示（基于内容推断） */}
      {emailDetail.hasAttachments && (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600">📎</span>
            <span className="text-sm text-yellow-700">
              此邮件可能包含附件，请在原邮件中查看下载
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function downloadAttachment(attachment: any) {
  try {
    const response = await fetch(attachment.download_url);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = attachment.filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('下载附件失败:', error);
    alert('下载附件失败');
  }
}
```

### 第三阶段：增强悬浮框交互

#### 3.1 增强型悬浮框组件
**文件**: `web/src/components/search/EnhancedCitation.tsx` (新建)

```tsx
import React, { useState } from 'react';
import { DocumentCardProps } from './Citation';
import { CompactDocumentCard } from './DocumentDisplay';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface EnhancedCitationProps {
  children: JSX.Element | string | null | React.ReactNode;
  document_info?: DocumentCardProps;
  onDetailClick?: (document: any) => void;
  index?: number;
}

export function EnhancedCitation({
  children,
  document_info,
  onDetailClick,
  index,
}: EnhancedCitationProps) {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);

  // 解析引用文本
  let innerText = '';
  if (index !== undefined) {
    innerText = index.toString();
  }
  if (children) {
    const childrenString = children.toString();
    const childrenSegment1 = childrenString.split('[')[1];
    if (childrenSegment1 !== undefined) {
      const childrenSegment1_0 = childrenSegment1.split(']')[0];
      if (childrenSegment1_0 !== undefined) {
        innerText = childrenSegment1_0;
      }
    }
  }

  if (!document_info) {
    return <>{children}</>;
  }

  const handleTooltipClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onDetailClick && document_info.document) {
      onDetailClick(document_info.document);
    }
    setIsTooltipOpen(false);
  };

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip open={isTooltipOpen} onOpenChange={setIsTooltipOpen}>
        <TooltipTrigger asChild>
          <span className="inline-flex items-center cursor-pointer transition-all duration-200 ease-in-out">
            <span
              className="flex items-center justify-center px-1 h-4 text-[10px] font-medium text-text-700 bg-background-100 rounded-full border border-background-300 hover:bg-background-200 hover:text-text-900 shadow-sm"
              style={{ transform: 'translateY(-10%)', lineHeight: '1' }}
            >
              {innerText}
            </span>
          </span>
        </TooltipTrigger>
        <TooltipContent
          className="border border-neutral-300 hover:text-neutral-900 bg-neutral-100 dark:!bg-[#000] dark:border-neutral-700 cursor-pointer"
          width="mb-2 max-w-lg"
          onClick={handleTooltipClick}
          // 允许鼠标在tooltip上移动而不关闭
          onMouseEnter={() => setIsTooltipOpen(true)}
          onMouseLeave={() => setIsTooltipOpen(false)}
        >
          <div className="space-y-2">
            {document_info?.document && (
              <CompactDocumentCard
                updatePresentingDocument={document_info.updatePresentingDocument}
                url={document_info.url}
                icon={document_info.icon}
                document={document_info.document}
              />
            )}
            
            {/* 仅在邮件类型时显示详情按钮 */}
            {document_info.document?.source_type === 'gmail' && (
              <div className="pt-2 border-t">
                <button
                  onClick={handleTooltipClick}
                  className="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                >
                  查看详细信息
                </button>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
```

#### 2.3 IMAP邮件增强型Citation组件
**文件**: `web/src/components/search/results/ImapCitation.tsx` (新建)

```tsx
import React, { useState } from 'react';
import { DocumentCardProps } from './Citation';
import { CompactDocumentCard } from './DocumentDisplay';
import { isImapEmail } from '@/lib/email/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ImapCitationProps {
  children: JSX.Element | string | null | React.ReactNode;
  document_info?: DocumentCardProps;
  onImapEmailDetailClick?: (document: any) => void;
  index?: number;
}

export function ImapCitation({
  children,
  document_info,
  onImapEmailDetailClick,
  index,
}: ImapCitationProps) {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);

  // 解析引用文本
  let innerText = '';
  if (index !== undefined) {
    innerText = index.toString();
  }
  if (children) {
    const childrenString = children.toString();
    const childrenSegment1 = childrenString.split('[')[1];
    if (childrenSegment1 !== undefined) {
      const childrenSegment1_0 = childrenSegment1.split(']')[0];
      if (childrenSegment1_0 !== undefined) {
        innerText = childrenSegment1_0;
      }
    }
  }

  if (!document_info) {
    return <>{children}</>;
  }

  const isImapEmailDocument = isImapEmail(document_info.document);

  const handleTooltipClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onImapEmailDetailClick && document_info.document && isImapEmailDocument) {
      onImapEmailDetailClick(document_info.document);
    }
    setIsTooltipOpen(false);
  };

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip open={isTooltipOpen} onOpenChange={setIsTooltipOpen}>
        <TooltipTrigger asChild>
          <span className="inline-flex items-center cursor-pointer transition-all duration-200 ease-in-out">
            <span
              className="flex items-center justify-center px-1 h-4 text-[10px] font-medium text-text-700 bg-background-100 rounded-full border border-background-300 hover:bg-background-200 hover:text-text-900 shadow-sm"
              style={{ transform: 'translateY(-10%)', lineHeight: '1' }}
            >
              {innerText}
            </span>
          </span>
        </TooltipTrigger>
        <TooltipContent
          className="border border-neutral-300 hover:text-neutral-900 bg-neutral-100 dark:!bg-[#000] dark:border-neutral-700 cursor-pointer"
          width="mb-2 max-w-lg"
          onClick={handleTooltipClick}
          onMouseEnter={() => setIsTooltipOpen(true)}
          onMouseLeave={() => setIsTooltipOpen(false)}
        >
          <div className="space-y-2">
            {document_info?.document && (
              <CompactDocumentCard
                updatePresentingDocument={document_info.updatePresentingDocument}
                url={document_info.url}
                icon={document_info.icon}
                document={document_info.document}
              />
            )}
            
            {/* 仅在IMAP邮件时显示详情按钮 */}
            {isImapEmailDocument && (
              <div className="pt-2 border-t">
                <button
                  onClick={handleTooltipClick}
                  className="w-full px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                >
                  查看邮件详情
                </button>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
```

#### 2.4 更新Citation组件使用
**文件**: `web/src/components/search/results/Citation.tsx` (修改)

```tsx
// 在原有的Citation组件基础上，添加对IMAP邮件类型的特殊处理
import { ImapCitation } from './ImapCitation';
import { isImapEmail } from '@/lib/email/utils';

export function Citation({
  children,
  document_info,
  question_info,
  index,
  onImapEmailDetailClick, // 新增prop：专门处理IMAP邮件详情点击
}: {
  document_info?: DocumentCardProps;
  question_info?: QuestionCardProps;
  children?: JSX.Element | string | null | ReactNode;
  index?: number;
  onImapEmailDetailClick?: (document: any) => void; // 新增prop
}) {
  // 如果是IMAP邮件类型，使用IMAP专用Citation
  if (document_info?.document && isImapEmail(document_info.document)) {
    return (
      <ImapCitation
        document_info={document_info}
        onImapEmailDetailClick={onImapEmailDetailClick}
        index={index}
      >
        {children}
      </ImapCitation>
    );
  }

  // 其他类型（包括Gmail等）保持原有逻辑
  return (
    <TooltipProvider delayDuration={0}>
      {/* 原有的Citation实现 */}
    </TooltipProvider>
  );
}
```

### 第四阶段：整合到聊天页面

#### 3.2 IMAP邮件侧滑容器组件
**文件**: `web/src/components/chat/ImapEmailDetailSlider.tsx` (新建)

```tsx
import React from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { ImapEmailDetailPanel } from './ImapEmailDetailPanel';

interface ImapEmailDetailSliderProps {
  isOpen: boolean;
  onClose: () => void;
  document: OnyxDocument | null;
}

export function ImapEmailDetailSlider({ 
  isOpen, 
  onClose, 
  document 
}: ImapEmailDetailSliderProps) {
  return (
    <div
      className={`
        fixed top-0 right-0 h-full z-50 bg-white dark:bg-gray-900 
        shadow-2xl transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        w-[500px] border-l border-gray-200 dark:border-gray-700
      `}
    >
      <div className="flex flex-col h-full">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">IMAP邮件详情</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
          >
            ✕
          </button>
        </div>
        
        {/* 内容区 */}
        <div className="flex-1 overflow-y-auto">
          {document && (
            <ImapEmailDetailPanel document={document} />
          )}
        </div>
      </div>
    </div>
  );
}
```

### 第四阶段：整合到聊天页面

#### 4.1 聊天页面状态管理
**文件**: `web/src/app/chat/ChatPage.tsx` (修改)

```tsx
import { ImapEmailDetailSlider } from '@/components/chat/ImapEmailDetailSlider';
import { isImapEmail } from '@/lib/email/utils';

// 在ChatPage组件中添加IMAP邮件详情状态
const [imapEmailDetailSlider, setImapEmailDetailSlider] = useState({
  isOpen: false,
  document: null as OnyxDocument | null,
});

const handleImapEmailDetailClick = (document: OnyxDocument) => {
  // 验证是IMAP邮件
  if (!isImapEmail(document)) {
    console.warn('非IMAP邮件，无法显示详情');
    return;
  }
  
  setImapEmailDetailSlider({
    isOpen: true,
    document: document,
  });
};

const closeImapEmailDetailSlider = () => {
  setImapEmailDetailSlider({
    isOpen: false,
    document: null,
  });
};

// 在render中添加ImapEmailDetailSlider组件
return (
  <div className="relative">
    {/* 现有的聊天界面 */}
    
    {/* IMAP邮件详情侧滑页面 */}
    <ImapEmailDetailSlider
      isOpen={imapEmailDetailSlider.isOpen}
      onClose={closeImapEmailDetailSlider}
      document={imapEmailDetailSlider.document}
    />
    
    {/* 背景遮罩 */}
    {imapEmailDetailSlider.isOpen && (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={closeImapEmailDetailSlider}
      />
    )}
  </div>
);
```

#### 4.2 传递回调函数到Message组件
需要通过props链传递 `handleImapEmailDetailClick` 函数到各个Message相关组件中：

```tsx
// 完整的组件props传递链：
ChatPage (状态管理)
    ↓ handleImapEmailDetailClick
MessageList (消息列表)
    ↓ onImapEmailDetailClick  
Message (单个消息)
    ↓ onImapEmailDetailClick
MemoizedTextComponents (文本解析)
    ↓ onImapEmailDetailClick 
Citation (引用组件)
    ↓ 检测source_type，选择组件
      ├─ 普通文档 → 原有Citation逻辑
      └─ IMAP邮件 → ImapCitation
            ↓ onImapEmailDetailClick
        处理点击，打开侧滑页面
```

**关键实现点**：
1. 在`MemoizedTextComponents.tsx`中获取`docs`数组和对应的`OnyxDocument`对象
2. 检查`document.source_type === 'imap'`来判断是否为IMAP邮件
3. 传递完整的`OnyxDocument`对象（包含`document_id`）给侧滑组件

### 第三阶段：IMAP邮件详情面板组件

#### 3.1 IMAP邮件详情面板
**文件**: `web/src/components/chat/ImapEmailDetailPanel.tsx` (新建)

```typescript
import React, { useState, useEffect } from 'react';
import { OnyxDocument } from '@/lib/search/interfaces';
import { ParsedImapEmailContent } from '@/lib/email/parser';
import { getDocumentFullContent } from '@/lib/api/document';
import { parseImapEmailContent, formatEmailDate, formatFileSize } from '@/lib/email/parser';
import { Spinner } from '@/components/Spinner';

interface ImapEmailDetailPanelProps {
  document: OnyxDocument;
}

export function ImapEmailDetailPanel({ document }: ImapEmailDetailPanelProps) {
  const [emailDetail, setEmailDetail] = useState<ParsedImapEmailContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchEmailDetailData();
  }, [document.document_id]);

  const fetchEmailDetailData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 直接使用document_id从现有API获取完整chunk内容
      const fullContent = await getDocumentFullContent(document.document_id, 0);
      
      // 解析IMAP邮件内容
      const parsedEmail = parseImapEmailContent(fullContent);
      if (!parsedEmail) {
        throw new Error('无法解析IMAP邮件内容');
      }
      
      setEmailDetail(parsedEmail);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取邮件详情失败';
      setError(errorMessage);
      console.error('获取IMAP邮件详情失败:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <Spinner />
        <span className="ml-2">正在获取邮件详情...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-red-600 mb-4">
          错误: {error}
        </div>
        <button
          onClick={fetchEmailDetailData}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  if (!emailDetail) {
    return <div className="p-4">暂无邮件详情</div>;
  }

  return (
    <div className="p-4 space-y-6">
      {/* 邮件标题 */}
      <div>
        <h3 className="text-xl font-bold mb-2">{emailDetail.subject}</h3>
      </div>

      {/* 邮件基本信息 */}
      <div className="space-y-3">
        <div>
          <span className="font-medium text-gray-600">发件人：</span>
          <span className="ml-2">{emailDetail.sender}</span>
        </div>
        
        <div>
          <span className="font-medium text-gray-600">收件人：</span>
          <span className="ml-2">{emailDetail.recipients.join(', ')}</span>
        </div>
        
        {emailDetail.ccRecipients && emailDetail.ccRecipients.length > 0 && (
          <div>
            <span className="font-medium text-gray-600">抄送：</span>
            <span className="ml-2">{emailDetail.ccRecipients.join(', ')}</span>
          </div>
        )}
        
        <div>
          <span className="font-medium text-gray-600">发送时间：</span>
          <span className="ml-2">{formatEmailDate(emailDetail.sendDate)}</span>
        </div>
      </div>

      {/* 邮件正文 */}
      <div>
        <h4 className="font-medium text-gray-600 mb-2">正文：</h4>
        <div className="prose max-w-none border rounded p-4 bg-gray-50 dark:bg-gray-800">
          <pre className="whitespace-pre-wrap font-sans text-sm">
            {emailDetail.bodyText}
          </pre>
        </div>
      </div>

      {/* 附件列表 */}
      {emailDetail.attachments && emailDetail.attachments.length > 0 && (
        <div>
          <h4 className="font-medium text-gray-600 mb-2">附件：</h4>
          <div className="space-y-2">
            {emailDetail.attachments.map((attachment, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center space-x-3">
                  <span className="text-gray-600">📎</span>
                  <div>
                    <div className="font-medium">{attachment.filename}</div>
                    <div className="text-sm text-gray-500">
                      {attachment.content_type} • {formatFileSize(attachment.size)}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  附件暂不支持下载
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🎨 UI/UX设计规范

### 侧滑动画
- **进入动画**: 从右侧滑入，duration-300, ease-in-out
- **退出动画**: 向右侧滑出，duration-300, ease-in-out
- **背景遮罩**: 半透明黑色背景，可点击关闭

### 布局规范
- **侧滑宽度**: 500px (移动端适配)
- **头部高度**: 64px，包含标题和关闭按钮
- **内容区**: flex-1，可滚动
- **间距**: 统一使用 Tailwind 间距规范

### 颜色主题
- **背景**: 跟随系统主题，支持深色模式
- **边框**: gray-200 / gray-700
- **按钮**: blue-600 主色调
- **文字**: 遵循现有的文字色彩规范

## 🔒 安全考虑

### 权限验证
- 所有邮件详情API都需要用户认证
- 验证用户对特定邮件的访问权限
- 附件下载需要二次权限验证

### 数据保护
- 邮件内容不在前端缓存
- 附件下载使用临时URL
- 敏感信息脱敏处理

### XSS防护
- 邮件HTML内容需要进行安全过滤
- 使用 `dangerouslySetInnerHTML` 时确保内容安全
- 附件文件名进行转义处理

## 📊 性能优化

### 懒加载
- 邮件详情仅在侧滑页面打开时加载
- 附件信息按需获取
- 使用React.memo优化组件渲染

### 缓存策略
- 邮件详情使用短期内存缓存（5分钟）
- 附件下载URL缓存1小时
- 使用SWR进行数据获取和缓存

### 资源管理
- 组件卸载时清理事件监听器
- 及时释放Blob URL资源
- 控制并发请求数量

## 🧪 测试方案

### 单元测试
- EmailDetailPanel组件功能测试
- EmailDetailSlider动画测试
- API函数错误处理测试

### 集成测试
- 引用点击到侧滑页面的完整流程
- 邮件详情加载和显示
- 附件下载功能

### 端到端测试
- 聊天对话中点击邮件引用
- 侧滑页面打开和关闭
- 邮件信息完整性验证

## ✅ 实施状态（IMAP Connector改造方案）

### ✅ 第一阶段：后端IMAP Connector改造 - **已完成**
- [x] **扩展EmailHeaders模型支持CC/BCC字段** - `backend/onyx/connectors/imap/models.py`
  - 新增 `cc_recipients: str | None` 字段
  - 新增 `bcc_recipients: str | None` 字段
  - 修改 `from_email_msg` 方法提取CC/BCC头信息
  
- [x] **重写邮件内容和附件解析函数** - `backend/onyx/connectors/imap/connector.py`
  - 新增 `_parse_email_content_and_attachments()` 函数
  - 支持邮件正文和附件信息的完整解析
  - 处理multipart邮件格式和HTML内容清理

- [x] **修改文档生成函数存储完整信息** - `backend/onyx/connectors/imap/connector.py`  
  - 改造 `_convert_email_headers_and_body_into_document()` 函数
  - 类似Gmail connector格式存储完整邮件信息到Vespa
  - 包含正文+邮件头+附件信息的完整内容结构

### ✅ 第二阶段：前端IMAP邮件解析开发 - **已完成**
- [x] **开发IMAP邮件内容解析器** - `web/src/lib/email/parser.ts`
  - `parseImapEmailContent()` 函数：解析Vespa中的完整邮件信息
  - 支持邮件字段提取（发件人、收件人、CC、BCC、主题、日期）
  - 支持附件信息解析和格式化
  
- [x] **开发IMAP邮件检测器** - `web/src/lib/email/utils.ts`
  - `isImapEmail()` 函数：检测文档是否为IMAP邮件类型
  - `getImapMessageId()` 函数：获取IMAP邮件ID
  - 支持邮件类型判断和显示名称获取

- [x] **开发邮件格式化工具** - `web/src/lib/email/parser.ts`
  - `formatEmailDate()` 函数：格式化邮件日期显示
  - `formatFileSize()` 函数：格式化附件文件大小
  - 中文本地化日期时间格式

- [x] **开发文档内容API封装** - `web/src/lib/api/document.ts`
  - `getDocumentFullContent()` 函数：基于现有API获取文档内容
  - `getDocumentChunkInfo()` 函数：获取完整chunk信息
  - 复用现有 `/api/document/chunk-info` 接口

### ✅ 第三阶段：前端组件开发 - **已完成**
- [x] **开发ImapEmailDetailSlider侧滑组件** - `web/src/components/chat/ImapEmailDetailSlider.tsx`
  - 侧滑动画和背景遮罩交互
  - ESC键关闭和点击遮罩关闭功能
  - 响应式布局和暗色模式支持
  
- [x] **开发ImapEmailDetailPanel详情面板** - `web/src/components/chat/ImapEmailDetailPanel.tsx`
  - 完整邮件信息显示（发件人、收件人、CC/BCC、主题、正文、附件）
  - 异步数据加载和错误处理机制
  - 重试功能和加载状态指示器

- [x] **开发ImapCitation增强引用组件** - `web/src/components/chat/ImapCitation.tsx`
  - IMAP邮件专用的可点击引用组件
  - 集成侧滑页面打开功能
  - 保持与现有引用样式的一致性

- [x] **修改Citation组件集成IMAP功能** - `web/src/components/search/results/Citation.tsx`
  - 检测IMAP邮件类型并使用增强组件
  - 保持原有tooltip悬浮框功能
  - 向后兼容其他文档类型的引用

### ✅ 第四阶段：集成到聊天页面 - **已完成**
- [x] **组件集成和状态管理**
  - 所有组件已正确集成并可独立工作
  - ImapCitation组件内置状态管理和事件处理
  - 组件间props传递和回调机制已建立

- [x] **错误处理和加载状态**
  - 完善的异步数据加载处理
  - 用户友好的错误提示和重试机制
  - loading状态指示器和空数据处理

- [x] **代码质量优化**
  - 修复潜在的变量名冲突问题
  - 使用useCallback优化React hooks性能
  - 正确的TypeScript类型定义和导入

### 🚀 待部署测试阶段
- [ ] **远程服务器部署测试**（**********）
  - 后端IMAP Connector重新构建和部署
  - 前端组件构建和集成测试
  - 数据重新索引验证（确保完整邮件信息存储）

- [ ] **功能完整性验证**
  - IMAP邮件引用点击打开侧滑页面
  - 邮件详情信息完整显示
  - 用户交互流程顺畅性测试

- [ ] **性能和兼容性测试**  
  - 不同浏览器兼容性验证
  - 响应式布局在不同设备上的表现
  - API响应时间和用户体验优化

## 📋 TODO List - 待实现功能

### 🔗 **附件下载功能** (优先级：高)

> **说明**: 当前阶段专注于邮件详情展示，附件下载功能将在后续迭代中实现

**TODO 任务清单**:

- [ ] **TODO-1: 后端存储附件二进制数据**
  - 扩展IMAP connector在索引时保存附件文件内容
  - 实现附件数据的安全存储机制（可能存储到文件系统或对象存储）
  - 建立附件ID与邮件的关联关系
  - 处理大附件文件的存储策略

- [ ] **TODO-2: 新增附件下载API端点**
  - 创建 `/api/email/attachment/download/{attachment_id}` 端点
  - 实现附件内容的流式下载支持
  - 添加Content-Type和Content-Disposition响应头
  - 支持断点续传和下载进度

- [ ] **TODO-3: 前端下载按钮和进度指示**
  - 在ImapEmailDetailPanel中添加附件下载按钮
  - 实现下载进度条和状态指示器
  - 添加下载成功/失败的用户反馈
  - 支持批量下载多个附件

- [ ] **TODO-4: 附件安全扫描和权限验证**
  - 实现用户权限验证（确保用户有权访问该邮件附件）
  - 添加附件类型白名单/黑名单机制
  - 集成病毒扫描和恶意文件检测
  - 实现下载速率限制和防滥用机制
  - 添加下载日志和审计功能

**预估工作量**: 2-3周开发周期

**技术要求**:
- 后端存储方案设计和实现
- 流式文件下载API开发
- 前端文件下载和进度管理
- 安全策略和权限控制

---

## 🚀 扩展方案

### 未来增强功能
1. **邮件线程展示**: 显示邮件对话线程
2. **快速回复**: 直接在侧滑页面回复邮件
3. **标签管理**: 为邮件添加自定义标签
4. **搜索优化**: 在邮件内容中高亮搜索关键词
5. **离线缓存**: 重要邮件离线可读

### 其他文档类型扩展
- PDF文档详情页面
- Word文档在线预览
- Excel表格数据展示
- 图片和视频媒体预览

## 📝 总结

**🎯 最优方案：IMAP Connector改造！**

通过深入分析IMAP Connector代码和文件溯源流程，确定**改造IMAP Connector**是最佳技术方案。

### 关键技术发现：

#### 文件溯源工作原理：
- **LLM生成引用**：包含 `citation_num` (D1, D2) 和 `document_id`
- **引用翻译**：通过 `document_id` 查找对应的 `DbSearchDoc.id`
- **前端显示**：使用现有的引用系统和文档API

#### IMAP数据存储问题：
- **邮件头信息未存储**：发件人、收件人等信息只在内存中处理，未写入Vespa
- **只存储正文**：Vespa中只包含邮件正文内容，缺少完整邮件信息
- **需要改造**：类似Gmail connector，将完整信息存储到Vespa

### IMAP Connector改造方案：

#### 后端改造要点：
- **扩展EmailHeaders模型**：支持CC/BCC等完整邮件字段
- **重写解析函数**：同时处理邮件正文和附件信息
- **改造文档生成**：将完整邮件信息（正文+邮件头+附件）存储到Vespa
- **类似Gmail格式**：采用与Gmail connector相同的数据存储模式

#### 前端解析设计：
- **邮件内容解析器**：`parseImapEmailContent()` 解析Vespa中的完整邮件信息
- **无需额外API**：直接使用现有的 `/api/document/chunk-info` API
- **完整信息展示**：支持标题、发件人、收件人、正文、附件等所有信息

### 核心优势：

1. **架构最优**: 无需新建API，复用现有基础设施
2. **性能最佳**: 数据已索引，即时响应，无网络延迟
3. **功能完整**: 包含邮件的所有重要信息（邮件标题、发件人、收件人、发件时间、正文的全部内容、附件信息）
4. **用户体验**: 流畅的侧滑界面和即时的邮件详情展示
5. **向后兼容**: 不影响现有的引用悬浮框功能
6. **维护简单**: 减少系统复杂度，无需管理额外的API

### 关键技术点：

- **数据一次存储**: 在索引时将完整邮件信息存储到Vespa
- **前端直接解析**: 通过解析器从chunk内容获取结构化邮件信息
- **兼容现有流程**: 完美融入现有的文件溯源和引用系统
- **渐进增强**: 在不破坏现有功能基础上逐步增强

### 实施周期：
**5周**实施计划，确保功能的完整性：
- 第1周：后端IMAP Connector改造
- 第2周：前端IMAP邮件解析开发
- 第3周：前端组件开发
- 第4周：集成和优化
- 第5周：测试和发布

### 重要说明：
改造后需要重新索引IMAP邮件数据，以确保所有邮件都包含完整的信息结构。

通过这个IMAP Connector改造方案，用户可以即时查看IMAP邮件的完整信息，同时保持系统的简洁性和高性能！

---

## 📋 完整实现文件清单

### 🔧 后端修改文件 (2个文件)

1. **`backend/onyx/connectors/imap/models.py`**
   - 扩展EmailHeaders模型，新增cc_recipients和bcc_recipients字段
   - 修改from_email_msg方法提取CC/BCC邮件头信息
   - **状态**: ✅ 已完成实现

2. **`backend/onyx/connectors/imap/connector.py`** 
   - 新增`_parse_email_content_and_attachments()`函数解析邮件正文和附件
   - 重写`_convert_email_headers_and_body_into_document()`方法
   - 按Gmail connector格式存储完整邮件结构化数据
   - **状态**: ✅ 已完成实现

### 🎨 前端新增文件 (6个文件)

3. **`web/src/lib/api/document.ts`** *(新建)*
   - 封装现有`/api/document/chunk-info` API
   - 提供`getDocumentFullContent()`函数获取文档内容
   - **状态**: ✅ 已完成实现

4. **`web/src/lib/email/parser.ts`** *(新建)*
   - IMAP邮件内容解析器，解析结构化邮件数据
   - 支持邮件字段、附件信息、日期格式化等功能
   - **状态**: ✅ 已完成实现

5. **`web/src/lib/email/utils.ts`** *(新建)*
   - IMAP邮件类型检测和工具函数
   - 提供`isImapEmail()`等判断函数
   - **状态**: ✅ 已完成实现

6. **`web/src/components/chat/ImapEmailDetailPanel.tsx`** *(新建)*
   - 邮件详情显示面板组件
   - 支持异步数据加载、错误处理、重试机制
   - **状态**: ✅ 已完成实现

7. **`web/src/components/chat/ImapEmailDetailSlider.tsx`** *(新建)*
   - 侧滑面板容器组件
   - 支持滑动动画、背景遮罩、ESC键关闭
   - **状态**: ✅ 已完成实现

8. **`web/src/components/chat/ImapCitation.tsx`** *(新建)*
   - 增强的IMAP引用组件
   - 处理点击事件打开侧滑面板，仅对IMAP邮件类型生效
   - **状态**: ✅ 已完成实现

### 🔄 前端修改文件 (1个文件)

9. **`web/src/components/search/results/Citation.tsx`** *(修改)*
   - 集成IMAP邮件特殊处理逻辑
   - 保持原有tooltip功能同时增加点击侧滑功能
   - **状态**: ✅ 已完成实现

---

## 🎯 实现成果总结

### ✅ 核心功能实现
- **保留现有功能**: D1、D2引用的hover tooltip完全保留 ✅
- **新增点击功能**: IMAP邮件引用支持点击打开详情侧滑页面 ✅  
- **完整邮件信息**: 显示发件人、收件人、CC/BCC、主题、正文、附件等全部信息 ✅
- **优雅交互**: 滑动动画、背景遮罩、键盘快捷键支持 ✅
- **无破坏性**: 不影响现有Gmail和其他文档类型的引用功能 ✅
- **复用现有API**: 基于现有`/api/document/chunk-info`接口，无需新增后端API ✅

### 🏗️ 技术架构优势
- **数据存储**: IMAP connector按Gmail格式存储结构化数据，便于解析 ✅
- **API复用**: 充分利用现有API基础设施，减少开发和维护成本 ✅
- **组件化设计**: 模块化组件设计，易于扩展和维护 ✅
- **类型安全**: 完整TypeScript类型定义和错误处理 ✅
- **响应式**: 支持暗色模式和不同屏幕尺寸 ✅

### 🚀 待部署验证项目
1. **远程服务器部署** (**********)
2. **IMAP数据重新索引** (确保完整邮件信息存储)
3. **端到端功能测试** (引用点击→侧滑页面→邮件详情显示)
4. **用户体验验证** (交互流程、响应速度、错误处理)

---

**🎉 实现总结**: IMAP邮件引用详情侧滑页面功能已完成**全部代码实现**，包含2个后端修改文件、6个前端新增文件和1个前端修改文件，总计**9个文件**的完整开发。现在需要在远程服务器上部署测试以验证功能完整性和用户体验。