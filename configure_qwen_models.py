#!/usr/bin/env python3
"""
配置 Qwen 自定义模型的脚本
用于在 Onyx 系统中注册 qwen3:32b 和 qwen2.5-32b-instruct 模型
"""

import requests
import json

# 配置参数
ONYX_BASE_URL = "http://localhost:3000"  # 修改为你的 Onyx 地址
ADMIN_EMAIL = "<EMAIL>"  # 修改为你的管理员邮箱
ADMIN_PASSWORD = "your-password"  # 修改为你的管理员密码

# Qwen API 配置
QWEN_API_BASE = "http://your-qwen-server:port/v1"  # 修改为你的 Qwen API 地址
QWEN_API_KEY = "your-qwen-api-key"  # 修改为你的 Qwen API Key

def login_admin():
    """管理员登录获取认证 token"""
    login_url = f"{ONYX_BASE_URL}/auth/login"
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    response = requests.post(login_url, json=login_data)
    if response.status_code == 200:
        # 从 cookies 中获取认证信息
        return response.cookies
    else:
        raise Exception(f"登录失败: {response.status_code} - {response.text}")

def create_qwen_provider(cookies):
    """创建 Qwen LLM 提供商"""
    provider_url = f"{ONYX_BASE_URL}/api/admin/llm/provider"
    
    provider_config = {
        "name": "qwen-custom",
        "provider": "openai",  # 使用 OpenAI 兼容接口
        "api_key": QWEN_API_KEY,
        "api_base": QWEN_API_BASE,
        "api_version": None,
        "custom_config": {},
        "default_model_name": "qwen3:32b",
        "fast_default_model_name": "qwen2.5-32b-instruct",
        "is_public": True,
        "groups": [],
        "deployment_name": None,
        "default_vision_model": None,
        "api_key_changed": True,
        "model_configurations": [
            {
                "name": "qwen3:32b",
                "max_input_tokens": 32768,
                "supports_image_input": False
            },
            {
                "name": "qwen2.5-32b-instruct",
                "max_input_tokens": 32768,
                "supports_image_input": False
            }
        ]
    }
    
    # 创建提供商
    response = requests.put(
        f"{provider_url}?is_creation=true",
        json=provider_config,
        cookies=cookies
    )
    
    if response.status_code == 200:
        print("✅ Qwen 提供商创建成功!")
        return response.json()
    else:
        raise Exception(f"创建提供商失败: {response.status_code} - {response.text}")

def set_as_default_provider(provider_id, cookies):
    """设置为默认提供商（可选）"""
    default_url = f"{ONYX_BASE_URL}/api/admin/llm/provider/{provider_id}/default"
    
    response = requests.post(default_url, cookies=cookies)
    
    if response.status_code == 200:
        print("✅ 已设置为默认提供商!")
    else:
        print(f"⚠️ 设置默认提供商失败: {response.status_code} - {response.text}")

def main():
    """主函数"""
    try:
        print("🔐 正在登录管理员账户...")
        cookies = login_admin()
        print("✅ 登录成功!")
        
        print("🚀 正在创建 Qwen 提供商...")
        provider = create_qwen_provider(cookies)
        print(f"✅ 提供商创建成功! ID: {provider.get('id')}")
        
        # 可选：设置为默认提供商
        # set_as_default_provider(provider.get('id'), cookies)
        
        print("\n🎉 配置完成! 现在可以使用以下模型:")
        print("- qwen3:32b")
        print("- qwen2.5-32b-instruct")
        
    except Exception as e:
        print(f"❌ 配置失败: {e}")

if __name__ == "__main__":
    main()
