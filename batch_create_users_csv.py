#!/usr/bin/env python3
"""
从CSV文件批量创建用户脚本
CSV文件格式: email,role (可选)
例如: <EMAIL>,basic
"""

import asyncio
import sys
import os
import csv
from typing import List, Tuple

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi_users.password import PasswordHelper
import uuid

from onyx.db.models import User
from onyx.auth.schemas import UserRole
from onyx.configs.app_configs import POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB

# 数据库连接配置
DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# 统一初始密码
INITIAL_PASSWORD = "Abc@123!"

class CSVUserBatchCreator:
    def __init__(self):
        self.engine = create_async_engine(DATABASE_URL)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self.password_helper = PasswordHelper()

    def read_users_from_csv(self, csv_file_path: str) -> List[Tuple[str, str]]:
        """从CSV文件读取用户信息"""
        users = []
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                # 尝试检测是否有标题行
                sample = file.read(1024)
                file.seek(0)
                
                # 如果第一行包含"email"，则跳过标题行
                has_header = 'email' in sample.lower()
                
                reader = csv.reader(file)
                if has_header:
                    next(reader)  # 跳过标题行
                
                for row_num, row in enumerate(reader, start=1):
                    if not row or not row[0].strip():
                        continue
                        
                    email = row[0].strip().lower()
                    role = row[1].strip().lower() if len(row) > 1 and row[1].strip() else 'basic'
                    
                    # 验证邮箱格式
                    if '@' not in email:
                        print(f"警告: 第{row_num}行邮箱格式无效: {email}")
                        continue
                    
                    # 验证角色
                    valid_roles = ['basic', 'admin', 'curator', 'global_curator']
                    if role not in valid_roles:
                        print(f"警告: 第{row_num}行角色无效: {role}，使用默认角色 basic")
                        role = 'basic'
                    
                    users.append((email, role))
                    
        except FileNotFoundError:
            print(f"错误: 找不到文件 {csv_file_path}")
            return []
        except Exception as e:
            print(f"读取CSV文件时发生错误: {e}")
            return []
        
        return users

    async def create_user_batch_from_csv(self, users: List[Tuple[str, str]], password: str):
        """从CSV数据批量创建用户"""
        async with self.async_session() as session:
            created_users = []
            failed_users = []
            
            for email, role in users:
                try:
                    # 检查用户是否已存在
                    # 使用简单查询检查
                    from sqlalchemy import select
                    result = await session.execute(select(User).where(User.email == email))
                    existing_user = result.scalar_one_or_none()
                    
                    if existing_user:
                        print(f"用户 {email} 已存在，跳过创建")
                        continue
                    
                    # 创建新用户
                    hashed_password = self.password_helper.hash(password)
                    
                    # 转换角色字符串为枚举
                    user_role = getattr(UserRole, role.upper(), UserRole.BASIC)
                    
                    user = User(
                        id=uuid.uuid4(),
                        email=email,
                        hashed_password=hashed_password,
                        is_active=True,
                        is_superuser=(role == 'admin'),
                        is_verified=True,
                        role=user_role,
                        shortcut_enabled=False,
                        chosen_assistants=None,
                        visible_assistants=[],
                        hidden_assistants=[],
                        pinned_assistants=None
                    )
                    
                    session.add(user)
                    created_users.append((email, role))
                    print(f"✓ 创建用户: {email} (角色: {role})")
                    
                except Exception as e:
                    failed_users.append((email, str(e)))
                    print(f"✗ 创建用户失败: {email}, 错误: {e}")
            
            try:
                await session.commit()
                print(f"\n成功创建 {len(created_users)} 个用户")
                if failed_users:
                    print(f"创建失败 {len(failed_users)} 个用户:")
                    for email, error in failed_users:
                        print(f"  - {email}: {error}")
                        
            except Exception as e:
                await session.rollback()
                print(f"批量创建失败，事务已回滚: {e}")

    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()

def create_sample_csv():
    """创建示例CSV文件"""
    sample_data = [
        ["email", "role"],
        ["<EMAIL>", "basic"],
        ["<EMAIL>", "basic"],
        ["<EMAIL>", "admin"],
        ["<EMAIL>", "curator"],
    ]
    
    with open("users_sample.csv", "w", newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerows(sample_data)
    
    print("已创建示例CSV文件: users_sample.csv")

async def main():
    print("=== Onyx CSV用户批量创建工具 ===")
    
    # 询问是否需要创建示例文件
    create_sample = input("是否需要创建示例CSV文件? (y/N): ")
    if create_sample.lower() == 'y':
        create_sample_csv()
        print("请编辑 users_sample.csv 文件，然后重新运行此脚本")
        return
    
    # 获取CSV文件路径
    csv_file = input("请输入CSV文件路径 (默认: users.csv): ").strip()
    if not csv_file:
        csv_file = "users.csv"
    
    creator = CSVUserBatchCreator()
    
    try:
        # 读取CSV文件
        users = creator.read_users_from_csv(csv_file)
        if not users:
            print("没有找到有效的用户数据")
            return
        
        print(f"从CSV文件读取到 {len(users)} 个用户")
        print(f"初始密码: {INITIAL_PASSWORD}")
        print()
        
        # 显示前几个用户作为预览
        print("用户预览:")
        for i, (email, role) in enumerate(users[:5]):
            print(f"  {i+1}. {email} ({role})")
        if len(users) > 5:
            print(f"  ... 还有 {len(users) - 5} 个用户")
        print()
        
        # 确认操作
        confirm = input("确认要执行批量用户创建吗? (y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
        
        # 批量创建用户
        print("开始批量创建用户...")
        await creator.create_user_batch_from_csv(users, INITIAL_PASSWORD)
        
        print("\n=== 创建完成 ===")
        print("所有用户的初始密码为:", INITIAL_PASSWORD)
        print("建议用户首次登录后修改密码")
        
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    finally:
        await creator.close()

if __name__ == "__main__":
    asyncio.run(main())