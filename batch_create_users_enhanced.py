#!/usr/bin/env python3
"""
增强版批量创建用户脚本
支持从CSV文件导入员工邮箱账号到Onyx知识管理系统
"""

import asyncio
import sys
import os
import csv
from typing import List, Dict
import pandas as pd

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
from fastapi_users.password import PasswordHelper
import uuid

from onyx.db.models import User
from onyx.auth.schemas import UserRole
from onyx.configs.app_configs import POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB

# 数据库连接配置
DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# 统一初始密码
INITIAL_PASSWORD = "Abc@123!"

class UserBatchCreator:
    def __init__(self):
        self.engine = create_async_engine(DATABASE_URL)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self.password_helper = PasswordHelper()

    def load_emails_from_csv(self, csv_file: str) -> List[str]:
        """从CSV文件加载邮箱列表"""
        emails = []
        try:
            df = pd.read_csv(csv_file)
            if 'email' in df.columns:
                emails = df['email'].dropna().tolist()
                print(f"从 {csv_file} 加载了 {len(emails)} 个邮箱")
            else:
                print("CSV文件中未找到 'email' 列")
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
        return emails

    async def check_existing_users(self, emails: List[str]) -> Dict[str, bool]:
        """检查哪些用户已存在"""
        async with self.async_session() as session:
            existing_users = {}
            for email in emails:
                result = await session.execute(
                    select(User).where(User.email == email.lower())
                )
                user = result.scalar_one_or_none()
                existing_users[email] = user is not None
            return existing_users

    async def create_user_batch(self, emails: List[str], password: str):
        """批量创建用户"""
        async with self.async_session() as session:
            created_users = []
            failed_users = []
            skipped_users = []
            
            for email in emails:
                try:
                    # 检查用户是否已存在
                    result = await session.execute(
                        select(User).where(User.email == email.lower())
                    )
                    existing_user = result.scalar_one_or_none()
                    
                    if existing_user:
                        print(f"⚠️  用户 {email} 已存在，跳过创建")
                        skipped_users.append(email)
                        continue
                    
                    # 创建新用户
                    hashed_password = self.password_helper.hash(password)
                    
                    user = User(
                        id=uuid.uuid4(),
                        email=email.lower(),
                        hashed_password=hashed_password,
                        is_active=True,
                        is_superuser=False,
                        is_verified=True,  # 预置用户直接设为已验证
                        role=UserRole.BASIC,  # 默认角色为基础用户
                        shortcut_enabled=False,
                        chosen_assistants=None,
                        visible_assistants=[],
                        hidden_assistants=[],
                        pinned_assistants=None
                    )
                    
                    session.add(user)
                    created_users.append(email)
                    print(f"✓ 创建用户: {email}")
                    
                except Exception as e:
                    failed_users.append((email, str(e)))
                    print(f"✗ 创建用户失败: {email}, 错误: {e}")
            
            try:
                await session.commit()
                print(f"\n=== 批量创建结果 ===")
                print(f"✓ 成功创建: {len(created_users)} 个用户")
                print(f"⚠️  跳过已存在: {len(skipped_users)} 个用户")
                print(f"✗ 创建失败: {len(failed_users)} 个用户")
                
                if failed_users:
                    print(f"\n失败详情:")
                    for email, error in failed_users:
                        print(f"  - {email}: {error}")
                        
            except Exception as e:
                await session.rollback()
                print(f"批量创建失败，事务已回滚: {e}")

    async def create_admin_user(self, email: str, password: str):
        """创建管理员用户"""
        async with self.async_session() as session:
            try:
                # 检查管理员是否已存在
                result = await session.execute(
                    select(User).where(User.email == email.lower())
                )
                existing_user = result.scalar_one_or_none()
                
                if existing_user:
                    print(f"管理员用户 {email} 已存在")
                    return
                
                hashed_password = self.password_helper.hash(password)
                
                admin_user = User(
                    id=uuid.uuid4(),
                    email=email.lower(),
                    hashed_password=hashed_password,
                    is_active=True,
                    is_superuser=True,
                    is_verified=True,
                    role=UserRole.ADMIN,
                    shortcut_enabled=False,
                    chosen_assistants=None,
                    visible_assistants=[],
                    hidden_assistants=[],
                    pinned_assistants=None
                )
                
                session.add(admin_user)
                await session.commit()
                print(f"✓ 创建管理员用户: {email}")
                
            except Exception as e:
                await session.rollback()
                print(f"✗ 创建管理员失败: {email}, 错误: {e}")

    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()

async def main():
    print("=== Onyx 增强版用户批量创建工具 ===")
    print(f"初始密码: {INITIAL_PASSWORD}")
    print()
    
    creator = UserBatchCreator()
    
    try:
        # 选择邮箱来源
        print("请选择邮箱来源:")
        print("1. 从CSV文件导入")
        print("2. 手动输入邮箱列表")
        
        choice = input("请输入选择 (1/2): ").strip()
        
        emails = []
        if choice == "1":
            csv_file = input("请输入CSV文件路径 (默认: employees.csv): ").strip()
            if not csv_file:
                csv_file = "employees.csv"
            emails = creator.load_emails_from_csv(csv_file)
        elif choice == "2":
            print("请输入邮箱列表 (每行一个，输入空行结束):")
            while True:
                email = input().strip()
                if not email:
                    break
                emails.append(email)
        else:
            print("无效选择，退出")
            return
        
        if not emails:
            print("没有找到邮箱，退出")
            return
        
        print(f"\n待创建用户数量: {len(emails)}")
        
        # 检查现有用户
        print("检查现有用户...")
        existing_users = await creator.check_existing_users(emails)
        existing_count = sum(existing_users.values())
        new_count = len(emails) - existing_count
        
        print(f"已存在用户: {existing_count} 个")
        print(f"需要创建: {new_count} 个")
        
        if new_count == 0:
            print("所有用户都已存在，无需创建")
            return
        
        # 确认操作
        confirm = input(f"\n确认要创建 {new_count} 个新用户吗? (y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
        
        # 首先创建一个管理员用户（如果需要）
        admin_email = input("请输入管理员邮箱 (留空跳过): ").strip()
        if admin_email:
            await creator.create_admin_user(admin_email, INITIAL_PASSWORD)
            print()
        
        # 批量创建普通用户
        print("开始批量创建用户...")
        await creator.create_user_batch(emails, INITIAL_PASSWORD)
        
        print("\n=== 创建完成 ===")
        print("所有用户的初始密码为:", INITIAL_PASSWORD)
        print("建议用户首次登录后修改密码")
        print("\n下一步:")
        print("1. 重启Onyx服务以确保配置生效")
        print("2. 通知用户使用邮箱和初始密码登录")
        print("3. 建议用户首次登录后立即修改密码")
        
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
    finally:
        await creator.close()

if __name__ == "__main__":
    asyncio.run(main())
